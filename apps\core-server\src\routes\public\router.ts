import { Router } from "express";
import { City, Country, State } from "country-state-city";
import { PublicController } from "./controller.js";
export function createCountriesRouter() {
  const router = Router();
  router.get("/get-countries", PublicController.getCountries);
  router.get("/get-states/:id", PublicController.getStatesByCountry);
  router.get(
    "/get-cities/:countryId/:stateId",
    PublicController.getCitiesByStateAndCountry,
  );
  router.get(
    "/get-points-conversion-rates",
    PublicController.getPointsConversationRates,
  );
  router.get("/faq", PublicController.getFaq);
  router.get("/customer-support", PublicController.getCustomerSupport);

  return router;
}
