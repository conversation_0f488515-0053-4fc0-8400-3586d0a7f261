import db, { Dr<PERSON>zleDB, Dr<PERSON>zleTransaction } from "@repo/db";
import { userDetails } from "@repo/db/schema";
import { eq } from "drizzle-orm";

function generateReferralCode() {
  const characters = "ABCDEFGHJKLMNOPQRSTUVWXYZ0123456789";
  const alphabets = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const numbers = "0123456789";
  // const shuffled = characters.split("").sort(() => 0.5 - Math.random());
  // return shuffled.slice(0, 8).join("");
  const shuffledNumbers = numbers.split("").sort(() => 0.5 - Math.random());
  const shuffledAlphabets = alphabets.split("").sort(() => 0.5 - Math.random());
  return (
    shuffledAlphabets.slice(0, 3).join("") +
    shuffledNumbers.slice(0, 3).join("")
  );
}

export async function suggestUniqueUsername(
  firstName: string,
  lastName: string,
  tx: DrizzleTransaction | DrizzleDB = db,
) {
  const maxAttempts = 5;
  let attempts = 0;

  // Generate a base username
  const baseUsername =
    `${firstName.toLowerCase()}${lastName.toLowerCase()}`.replace(
      /[^a-z0-9]/g,
      "",
    ); // Remove special characters
  let suggestedUsername = baseUsername;

  while (attempts < maxAttempts) {
    // Check if the username exists in the database
    const existing = await tx
      .select()
      .from(userDetails)
      .where(eq(userDetails.userName, suggestedUsername))
      .limit(1);

    if (existing.length === 0) {
      return suggestedUsername; // Unique username found
    }

    // Generate a new suggestion by appending a random number
    const randomSuffix = Math.floor(1000 + Math.random() * 9000); // Random 4-digit number
    suggestedUsername = `${baseUsername}${randomSuffix}`;

    attempts++;
  }

  throw new Error("Failed to suggest a unique username after maximum attempts");
}
