import { Router } from "express";
import { PostsController } from "./controller.js";

export function createPostsRouter() {
  const router = Router();
  router.get("/", PostsController.getPosts);
  router.post("/create", PostsController.createPost);
  router.post("/edit/:id", PostsController.editPost);
  router.post("/delete/:id", PostsController.deletePost);
  router.get("/:id", PostsController.getPostById);
  return router;
}
