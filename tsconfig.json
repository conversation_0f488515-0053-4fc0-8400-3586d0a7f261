{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    "esModuleInterop": true,
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "types": ["react-native"],
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "isolatedModules": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "incremental": true,
    "jsx": "react-native",
    "moduleDetection": "force",
    "customConditions": ["react-native"],
    "noEmit": true,
    "skipLibCheck": true,
    "target": "ESNext",
    "baseUrl": ".",
    "experimentalDecorators":  true,// Required for WatermelonDB decorators
    "emitDecoratorMetadata": true, // Required for WatermelonDB decorators
    "paths": {
      "@components/*": ["./src/components/*"],
      "@config/*": ["./src/config/*"],
      "@constants/*": ["./src/constants/*"],
      "@context/*": ["./src/context/*"],
      "@features/*": ["./src/features/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@navigation/*": ["./src/navigation/*"],
      "@packages/*": ["./src/packages/*"],
      "@/types/*": ["./src/types/*"],
      "@src/*": ["./src/*"],
      "@assets/*": ["./assets/*"] // Outer
    }
  },
  "include": ["src/navigation/**/*", "src/**/*", "tests/**/*", "assets", "__mocks__/**/*", "scripts/**/*", "declarations.d.ts"],
  "exclude": ["node_modules", "dist", "build", "web", "**/*.nitro"]
}
