import { referralTable, referralUsesTable, userDetails } from "@repo/db/schema";
import { RepoPayload } from "./types.js";
import { and, eq, gt } from "drizzle-orm";
import { decrement, DrizzleDB, DrizzleTransaction } from "@repo/db";

// TODO: use this method to generate referral code, pass nothing for your usecase of unique referral for each user
export async function createReferral(
  payload: RepoPayload<{ issuer?: string; usesAvailable?: number }>
) {
  const referralCode = generateReferralCode();

  return payload.tx
    .insert(referralTable)
    .values({
      issuer: payload.data.issuer,
      usesAvailable: payload.data.usesAvailable,
      referralCode: referralCode,
    })
    .returning();
}

export async function useReferral(
  payload: RepoPayload<{ userId: string; referralCode: string }>
) {
  const [referral] = await payload.tx
    .select()
    .from(referralTable)
    .where(
      and(
        eq(referralTable.referralCode, payload.data.referralCode),
        gt(referralTable.usesAvailable, 0)
      )
    )
    .limit(1);

  if (!referral) return null;

  await payload.tx
    .update(referralTable)
    .set({ usesAvailable: decrement(referralTable.usesAvailable, 1) })
    .where(eq(referralTable.id, referral.id));

  await payload.tx.insert(referralUsesTable).values({
    usedBy: payload.data.userId,
    referralCode: payload.data.referralCode,
  });

  return referral;
}

function generateReferralCode() {
  const characters = "ABCDEFGHJKLMNOPQRSTUVWXYZ0123456789";
  const alphabets = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const numbers = "0123456789";
  // const shuffled = characters.split("").sort(() => 0.5 - Math.random());
  // return shuffled.slice(0, 8).join("");
  const shuffledNumbers = numbers.split("").sort(() => 0.5 - Math.random());
  const shuffledAlphabets = alphabets.split("").sort(() => 0.5 - Math.random());
  return (
    shuffledAlphabets.slice(0, 3).join("") +
    shuffledNumbers.slice(0, 3).join("")
  );
}

export async function createUniqueReferralCode(
  tx: DrizzleTransaction | DrizzleDB
) {
  let attempts = 0;
  const maxAttempts = 5;

  while (attempts < maxAttempts) {
    const code = generateReferralCode();

    // Check if code exists
    const existing = await tx
      .select()
      .from(referralTable)
      .where(eq(referralTable.referralCode, code))
      .limit(1);

    if (existing.length === 0) {
      return code;
    }

    attempts++;
  }

  throw new Error(
    "Failed to generate unique referral code after maximum attempts"
  );
}
