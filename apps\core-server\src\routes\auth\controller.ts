import {
  authValidator,
  bodyValidator,
  mediaBodyValidator,
} from "@/lib/validation-module.js";
import type { Infer } from "@vinejs/vine/types";
import type { Request, Response, NextFunction } from "express";
import {
  validateLogin,
  validateSignup,
  validateAuthVerify,
} from "./validator.js";
import { HashingModule } from "@/lib/hashing-module.js";
import { TokenModule } from "@repo/lib";
import { and, eq, or, desc } from "drizzle-orm";
import { constants, getCookieOptions } from "@/lib/cookie-module.js";
import { OTPModule } from "@/lib/otp-module.js";
import db from "@repo/db";
import { addressTable, avatarTable, userDetails, users } from "@repo/db/schema";
import { ErrorResponse } from "@/lib/error-handlers-module/index.js";
import { suggestUniqueUsername } from "./lib.js";
import { UserRepo } from "@repo/repo";

// TODO: convert email to lowercase

export class AuthController {
  @bodyValidator(validateSignup)
  static async signup(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof validateSignup>;

      const existingUser = await db.query.users.findFirst({
        where: (data, { eq }) => eq(data.email, payload.email),
      });
      if (!existingUser) {
        await db.insert(users).values([{ email: payload.email }]);
      } else if (existingUser && existingUser.verified) {
        res
          .status(409)
          .json({ message: "Account already exists, try logging in" });
        return;
      } else {
      }

      if (process.env.NODE_ENV !== "development") {
        await OTPModule.sendMailOTP({ email: payload.email });
      }

      res.status(202).json({ message: "proceed with otp" });
      return;
    } catch (error) {
      next(error);
    }
  }
  @bodyValidator(validateLogin)
  static async login(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof validateSignup>;

      const existingUser = await db.query.users.findFirst({
        where: (data, { eq }) => eq(data.email, payload.email),
      });
      if (!existingUser || !existingUser.verified) {
        res.status(404).json({ message: "User not found, Please sign up" });
        return;
      }

      if (process.env.NODE_ENV !== "development") {
        await OTPModule.sendMailOTP({ email: payload.email });
      }
      res.status(202).json({ message: "proceed with otp" });
      return;
    } catch (error) {
      next(error);
    }
  }

  @bodyValidator(validateAuthVerify)
  static async verify(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof validateAuthVerify>;

      const verified = await OTPModule.verifyMailOTP(
        payload.email,
        payload.otp,
      );
      if (!verified) {
        res.status(401).json({ message: "invalid otp" });
        return;
      }

      // let userResponse = {} as any;
      const user = await UserRepo.getPersonalUser({
        email: payload.email,
      });

      if (!user) {
        res.status(409).json({ message: "user not found" });
        return;
      }

      const refreshToken = TokenModule.signRefreshToken({
        email: payload.email,
        userID: user.id.toString(),
      });
      const accessToken = TokenModule.signAccessToken({
        email: payload.email,
        userID: user.id.toString(),
      });

      await db
        .update(users)
        .set({
          refreshToken: refreshToken,
          lastLoginAt: new Date(),
        })
        .where(eq(users.email, payload.email));

      if (!user.verifiedAt || !user.verified) {
        await db.transaction(async (tx) => {
          await tx
            .update(users)
            .set({
              verifiedAt: new Date(),
              verified: true,
            })
            .where(eq(users.email, payload.email));
        });
      }
      const suggestedName = await suggestUniqueUsername(payload.email);

      res.cookie(...getCookieOptions(refreshToken));
      res.json({
        data: {
          accessToken,
          refreshToken,
          user: !user.username ? null : user,
          email: payload.email,
          isSignupCompleted: !user.username ? false : true,
          username: suggestedName,
        },
        message: "User verified successfully",
        success: true,
      });

      return;
    } catch (error) {
      next(error);
    }
  }

  static async refetchAccessToken(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const refreshToken =
        req.body.refreshToken || req.cookies[constants.refreshToken.cookie];
      if (!refreshToken) {
        res.status(401).json({ message: "Missing refresh token" });
        return;
      }
      const payload = TokenModule.verifyRefreshToken(refreshToken);
      if (!payload) {
        res.status(401).json({ message: "Invalid refresh token" });
        return;
      }
      const user = await UserRepo.getPersonalUser({
        refreshToken: refreshToken,
        email: payload.email,
      });

      if (!user) {
        res.status(401).json({ message: "Discarded refresh token" });
        return;
      }
      await db
        .update(users)
        .set({
          lastLoginAt: new Date(),
        })
        .where(eq(users.email, payload.email));

      const accessToken = TokenModule.signAccessToken({
        email: payload.email,
        userID: payload.userID,
      });
      const suggestedName = await suggestUniqueUsername(payload.email);
      res.json({
        data: {
          accessToken,
          user: !user.username ? null : user,
          email: user.email,
          isSignupCompleted: !user.username ? false : true,
          refreshToken,
          username: suggestedName,
        },
        success: true,
      });
      return;
    } catch (error) {
      next(error);
    }
  }

  @authValidator()
  static async logout(req: Request, res: Response, next: NextFunction) {
    try {
      const refreshToken =
        req.body.refreshToken || req.cookies[constants.refreshToken.cookie];
      if (!refreshToken) {
        res.sendStatus(401);
        return;
      }
      const payload = TokenModule.verifyRefreshToken(refreshToken);
      if (!payload) {
        res.status(401).json({ message: "invalid refresh token" });
        return;
      }

      await db.transaction(async (tx) => {
        await tx
          .update(users)
          .set({
            refreshToken: null,
          })
          .where(eq(users.email, payload.email));
        await tx
          .update(userDetails)
          .set({ fcmToken: null })
          .where(eq(userDetails.userId, payload.userID));
      });

      res.clearCookie(constants.refreshToken.cookie, {
        domain: constants.refreshToken.cookieDomain,
      });
      res.json({ message: "Logged out", success: true });

      return;
    } catch (error) {
      res.clearCookie(constants.refreshToken.cookie, {
        domain: constants.refreshToken.cookieDomain,
      });
      next(error);
    }
  }
}
