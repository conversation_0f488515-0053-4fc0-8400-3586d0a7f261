import db, { Dr<PERSON>zleDB, DrizzleTransaction, objectBuilder } from "@repo/db";
import { optionsTable, questionsTable, userAnswers } from "@repo/db/schema";
import { and, asc, eq, sql } from "drizzle-orm";
import { pgTable, uuid, text, pgEnum, timestamp } from "drizzle-orm/pg-core";

export const questionTypeEnum = pgEnum("question_type_enum", ["multiple-select", "single-select"]);

export class OnboardRepo {
  static async createQuestion(
    payload: {
      title: string;
      description: string;
      questionType: "multiple-select" | "single-select";
      options: string[];
    },
    tx: DrizzleTransaction | DrizzleDB = db
  ) {
    const [question] = await tx
      .insert(questionsTable)
      .values({
        title: payload.title,
        description: payload.description,
        type: payload.questionType
      })
      .returning();
    if (!question) throw new Error("Failed to create question");

    await tx
      .insert(optionsTable)
      .values(payload.options.map((option, i) => ({ option, order: i, questionId: question.id })));
  }

  static async addOptions(
    payload: { questionId: string; options: string[] },
    tx: DrizzleTransaction | DrizzleDB = db
  ) {
    await tx
      .insert(optionsTable)
      .values(
        payload.options.map((option, i) => ({ questionId: payload.questionId, option, order: i }))
      );
  }

  static async answerQuestion(
    payload: { questionId: string; userId: string; answers: string[] },
    tx: DrizzleTransaction | DrizzleDB = db
  ) {
    await tx.insert(userAnswers).values({
      userId: payload.userId,
      questionId: payload.questionId,
      answers: payload.answers
    });
  }

  static async getQuestions(tx: DrizzleTransaction | DrizzleDB = db) {
    return await tx
      .select({
        id: questionsTable.id,
        title: questionsTable.title,
        description: questionsTable.description,
        type: questionsTable.type,
        options: objectBuilder(
          {
            id: optionsTable.id,
            option: optionsTable.option
          },
          true,
          optionsTable.order
        )
      })
      .from(questionsTable)
      .leftJoin(optionsTable, eq(optionsTable.questionId, questionsTable.id))
      .orderBy(asc(questionsTable.order))
      .groupBy(questionsTable.id, optionsTable.questionId);
  }

  static async getUserAnswers(userId: string, tx: DrizzleTransaction | DrizzleDB = db) {
    const answers = await tx
      .select({
        id: questionsTable.id,
        title: questionsTable.title,
        description: questionsTable.description,
        type: questionsTable.type,
        // this needs to be { id: optionsTable.id, option: optionsTable.option,selected:true or false }
        options: sql<
          {
            id: string;
            option: string;
            selected: boolean;
          }[]
        >`json_agg(jsonb_build_object('id', ${optionsTable.id}, 'option', ${optionsTable.option}, 'selected', ${optionsTable.id}=any(${userAnswers.answers})))`
      })
      .from(questionsTable)
      .leftJoin(optionsTable, eq(optionsTable.questionId, questionsTable.id))
      .innerJoin(
        userAnswers,
        and(eq(userAnswers.questionId, questionsTable.id), eq(userAnswers.userId, userId))
      )
      .groupBy(questionsTable.id, optionsTable.questionId);
    //   answers[0]?.answers
    return answers;
  }
}
