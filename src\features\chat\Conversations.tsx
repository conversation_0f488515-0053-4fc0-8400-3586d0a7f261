import {ThemeColors} from '@/types/color-types';
import PinIcon from '@assets/svgs/pin-icon.svg';
import SearchIcon from '@assets/svgs/search-icon.svg';
import {Button, Text, View} from '@components/native';
import {useDialog} from '@components/shared';
import {BounceTap, Ripple} from '@components/shared/animated';
import {useTheme} from '@context/index';
import {router} from '@navigation/refs/navigation-ref';
import {FlashList} from '@shopify/flash-list';
import React from 'react';
import {Dimensions, FlatList} from 'react-native';

const {width} = Dimensions.get('screen');

const mockBestStichObj = {
  username: '@saintbyheart',
  totalHealCount: 20,
} as const;

type Stitcher = typeof mockBestStichObj;

const mockChatItemObj = {
  name: 'SU101',
  lastMsg: 'Stand up for what you believe in you believe in asdas',
  unreadMsgCount: 2,
  pinned: true,
  isOnline: true,
} as const;

type ChatItem = typeof mockChatItemObj;

export const ConversationsScreen = () => {
  const {colors} = useTheme();

  return (
    <>
      <View flex={1} bg="background">
        <FlashList
          onRefresh={() => {}}
          ItemSeparatorComponent={() => <View h={8} />}
          refreshing={false}
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={ConversationsHeader}
          data={Array.from({length: 10}).fill(mockChatItemObj)}
          contentContainerStyle={{
            paddingBottom: 80,
          }}
          renderItem={({item, index}) => <ChatItem colors={colors} item={item as any} key={index} />}
        />
      </View>
    </>
  );
};

const ConversationsHeader = () => {
  const {colors} = useTheme();
  const {openDialog, Dialog} = useDialog();

  return (
    <View bg="white">
      <View p={20}>
        <Ripple
          onPress={() =>
            // Show confirmation dialog before saving
            openDialog(
              <View style={{alignItems: 'center'}}>
                <Text style={{fontSize: 18, marginBottom: 20}}>Save journal ?</Text>
                {/* <Button
                  title="Confirm"
                  onPress={() => {
                    mutate(formData);
                  }}
                /> */}
              </View>,
            )
          }>
          <View bg="white" bc="purpleLight" bw={1} display="flex" fd="row" jc="space-between" br={16} px={20} py={14}>
            <Text color="neutral40" fs="14">
              Search
            </Text>
            <SearchIcon stroke={colors.purple600} />
          </View>
        </Ripple>
        <Text mt={20} fs="14" fw="600" color="neutral70">
          Best Stitchers
        </Text>
      </View>
      <FlatList
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        pagingEnabled
        contentContainerStyle={{paddingHorizontal: 20, marginBottom: 20}}
        ItemSeparatorComponent={() => <View w={12} />}
        data={Array.from({length: 20}).fill(mockBestStichObj) as any}
        renderItem={({item, index}: {item: Stitcher; index: number}) => {
          return (
            <BounceTap onPress={() => {}} pressedScale={0.97} key={index}>
              <View shadow="md" bg="white" mih={120} br={14} bc="neutral10" bw={1} mah={180} miw={130} maw={150} py={10} display="flex" fd="column" jc="center" ai="center">
                <View>
                  <View size={64} mb={20} br={100} bg="orange" />
                  <View bg="lightBlue" pos="absolute" bottom={2} px={5} py={3} br={7} bw={1} bc="neutral20">
                    <Text fs="10">{item.totalHealCount.toString()} Healed</Text>
                  </View>
                </View>
                <View mt={5} px={10}>
                  <Text numberOfLines={1} fs="12" fw="500" color="neutral80">
                    {item.username}
                  </Text>
                </View>
                <View flexCenterRow mt={10} px={10}>
                  <Button h={32} py={0} bg="purpleLight" br={10} color="neutral80" isHorizontalAligned onPress={() => {}}>
                    Chat
                  </Button>
                </View>
              </View>
            </BounceTap>
          );
        }}
      />
      <View px={20} mb={10}>
        <Text fs="14" fw="600" color="neutral80">
          Recent stitching
        </Text>
      </View>
    </View>
  );
};

const AVATAR_SIZE = 45;

type ChatItemProps = {
  item: ChatItem;
  colors: ThemeColors;
};
const ChatItem: React.FC<ChatItemProps> = ({item, colors}) => {
  return (
    <Ripple onPress={() => router.navigate('Chat')} rippleColor={colors.neutral10}>
      <View disableSizeMatter display="flex" fd="row" ai="center" w={width} mih={50} mah={80} px={20} py={10}>
        <View size={AVATAR_SIZE} br={100} bg="orange" />
        <View disableSizeMatter flex={1} display="flex" fd="row" jc="space-between" ai="center" px={10} gap={10}>
          <View
            display="flex"
            fd="column"
            gap={4}
            flexGrow={1}
            flexShrink={1}
            maw={width - AVATAR_SIZE - 80} // Account for avatar, padding, and icons
          >
            <Text color="neutral80" fs="12" fw="600">
              {item.name}
            </Text>
            <Text numberOfLines={1} color="neutral50" fs="14">
              {item.lastMsg}
            </Text>
          </View>
          <View disableSizeMatter display="flex" fd="row" ai="center" gap={10}>
            {item.unreadMsgCount > 0 && (
              <View size={20} flexCenterRow br={100} bg="orange">
                <Text color="neutral80" fs="12">
                  {item.unreadMsgCount}
                </Text>
              </View>
            )}
            {item.pinned && <PinIcon />}
          </View>
        </View>
      </View>
    </Ripple>
  );
};
