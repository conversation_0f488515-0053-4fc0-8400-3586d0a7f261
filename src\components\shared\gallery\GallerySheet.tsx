// src/components/MediaGallerySheet.tsx
import React, {FC, useEffect, useState} from 'react';
import {Pressable, StyleSheet, Dimensions, ScrollView, PermissionsAndroid, Platform} from 'react-native';
import {useTheme} from '@context/index';
import {BounceTap} from '../animated';
import GalleryIcon from '@assets/svgs/gallery-icon.svg';
import {FlashList} from '@shopify/flash-list';
import Animated, {useSharedValue, useAnimatedStyle, withTiming, useAnimatedScrollHandler} from 'react-native-reanimated';
import {useBottomSheet} from '../bottom-sheet/BottomSheet';
import {request, requestMultiple, PERMISSIONS} from 'react-native-permissions';
import {Album, CameraRoll, PhotoIdentifier} from '@react-native-camera-roll/camera-roll';
import {BottomSheetFlashList} from '@gorhom/bottom-sheet';
import {Text, View} from '@components/native';

const {width} = Dimensions.get('window');
const ITEM_SIZE = (width - 40) / 3;

export const MediaGallerySheet: FC = () => {
  const {colors} = useTheme();
  const {Sheet, openSheet} = useBottomSheet({
    snapPoints: ['60%', '100%'],
  });
  const [albums, setAlbums] = useState<Album[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [images, setImages] = useState<PhotoIdentifier[]>([]);

  useEffect(() => {
    (async () => {
      let perm;
      if (Platform.OS == 'ios') {
        perm = await request(PERMISSIONS.IOS.PHOTO_LIBRARY);
      } else if (Platform.OS == 'android') {
        if (Platform.Version >= 33) {
          perm = await requestMultiple([PERMISSIONS.ANDROID.READ_MEDIA_IMAGES, PERMISSIONS.ANDROID.READ_MEDIA_VIDEO]);
        } else {
          perm = await request(PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE);
        }
      }
      if (perm == 'granted' || perm == 'limited') {
        const albums = await CameraRoll.getAlbums({assetType: 'Photos', albumType: 'All'});
        setAlbums(albums);
        if (albums.length > 0) {
          const images = await CameraRoll.getPhotos({groupName: albums[0].type, assetType: 'Photos', first: albums[0].count});
          setImages(images.edges);
        }
        setLoading(false);
      } else {
        setLoading(false);
        return;
      }
    })();
  }, []);

  useEffect(() => {
    if (albums.length > 0) {
    }
  }, [activeTab]);

  const GalleryTabContent = ({assets}: {assets: PhotoIdentifier[]}) => {
    const scale = useSharedValue(1);
    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{scale: withTiming(scale.value, {duration: 200})}],
    }));

    // const scrollHandler = useAnimatedScrollHandler(event => {
    //   scrollY. = event.contentOffset.y;
    // });
    console.log('>>IMages ', images.length);
    return (
      <Animated.View style={{flex: 1, backgroundColor: 'red'}}>
        <FlashList
          data={assets}
          keyExtractor={item => item.node.id}
          renderItem={({item}) => (
            <View style={{height: 200, width: 200, backgroundColor: 'red'}}></View>
            // <Pressable
            //   onPressIn={() => (scale.value = 0.95)}
            //   onPressOut={() => (scale.value = 1)}>
            //   <Animated.View style={[animatedStyle, styles.imageContainer]}>
            //     <FastImage
            //       // defaultSource={item.node.location}
            //       // source={{uri: item.node.location}}

            //       style={styles.image}
            //       resizeMode="cover"
            //     />
            //   </Animated.View>
            // </Pressable>
          )}
          numColumns={3}
          estimatedItemSize={ITEM_SIZE}
          contentContainerStyle={styles.listContent}
          // onScroll={scrollHandler}
          scrollEventThrottle={16}
        />
      </Animated.View>
    );
  };

  const CustomTabBar = () => {
    const tabScrollX = useSharedValue(0);
    const animatedTabStyle = useAnimatedStyle(() => ({
      transform: [{translateX: withTiming(tabScrollX.value, {duration: 200})}],
    }));

    return (
      <View style={styles.tabBarContainer}>
        <Animated.View style={[styles.tabIndicator, animatedTabStyle]} />
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}>
          {albums.map((album, i) => (
            <Pressable
              key={album.id}
              style={[styles.tabItem, i === activeTab && styles.tabItemActive]}
              onPress={() => setActiveTab(i)}>
              <Text style={[styles.tabLabel, i === activeTab && {color: colors.purple700}]}>{album.title}</Text>
            </Pressable>
          ))}
        </ScrollView>
        <GalleryTabContent assets={images} />
      </View>
    );
  };

  return (
    <>
      <BounceTap onPress={() => openSheet()}>
        <GalleryIcon stroke={colors.purple700} />
      </BounceTap>

      <Sheet title="Gallery">
        {loading ? (
          <Text style={[styles.centerText, {color: colors.text}]}>Loading gallery...</Text>
        ) : (
          <BottomSheetFlashList
            scrollEnabled
            ListHeaderComponent={<CustomTabBar />}
            data={images}
            estimatedItemSize={images.length}
            renderItem={({item}) => {
              return (
                <View
                  h={200}
                  w={200}
                  bg="negative30"
                  mb={20}
                />
              );
            }}
          />
        )}

        {/* <GalleryTabContent assets={[]} /> */}
        {/* TODO: fetch assets per activeTab with MediaLibrary.getAssetsAsync({ album: albums[activeTab] }) */}
      </Sheet>
    </>
  );
};

const styles = StyleSheet.create({
  sheetContent: {flex: 1, paddingHorizontal: 10},
  centerText: {textAlign: 'center', marginTop: 20},
  title: {fontSize: 24, fontWeight: 'bold', marginBottom: 10, textAlign: 'center'},
  tabBarContainer: {height: 50, marginBottom: 10},
  tabIndicator: {position: 'absolute', bottom: 0, height: 2, width: 100, backgroundColor: '#604AE6'},
  tabItem: {paddingHorizontal: 10, paddingVertical: 10, alignItems: 'center'},
  tabItemActive: {borderBottomWidth: 2, borderBottomColor: '#604AE6'},
  tabLabel: {fontSize: 14, color: '#888'},
  imageContainer: {margin: 2, borderRadius: 8, overflow: 'hidden'},
  image: {width: ITEM_SIZE, height: ITEM_SIZE},
  listContent: {paddingVertical: 10},
});

export default MediaGallerySheet;
