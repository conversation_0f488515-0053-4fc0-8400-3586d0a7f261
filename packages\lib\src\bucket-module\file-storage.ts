import type { UploadedFile } from "express-fileupload";
import fs from "fs";
import path from "path";

import { logger, Random } from "../index.js";

export default class FileBasedBucket {
  static async upload(file: UploadedFile, subpath?: string) {
    const fileName = Random.generateString(4, "hex") + path.extname(file.name);
    const filePath = path.join(
      process.cwd(),
      "storage",
      subpath ?? "",
      fileName
    );
    await FileBasedBucket.createDirectoryIfNotExists(
      `storage/${subpath ?? ""}`
    );
    file.mv(filePath);
    const publicUrl = await FileBasedBucket.getPublicUrl(
      `${subpath ?? ""}/${fileName}`
    );
    return publicUrl;
  }
  static async deleteFile(url: string) {
    const filePath = await FileBasedBucket.getPath(url);
    try {
      fs.unlinkSync(filePath);
    } catch (error) {
      logger.error("Error deleting file:", error);
    }
  }

  static async getPath(url: string) {
    const path = url.replace(
      `${process.env.SERVER_URL}/files/`,
      `${process.cwd()}/storage/`
    );
    return path;
  }

  static async getPublicUrl(key: string) {
    return path.join(process.env.SERVER_URL!, "/files", key);
  }

  static async createDirectoryIfNotExists(path: string) {
    return new Promise<boolean>((resolve, reject) => {
      if (fs.existsSync(path)) {
        resolve(true);
      } else {
        fs.mkdir(path, { recursive: true }, (err) => {
          if (err) {
            reject(err);
          }
          resolve(true);
        });
      }
    });
  }
}
