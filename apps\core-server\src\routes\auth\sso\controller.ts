import type { Request, Response, NextFunction } from "express";
import { AppleAuthValidator, GoogleAuthValidator } from "./lib.js";
import db from "@repo/db";
import { addressTable, avatarTable, userDetails, users } from "@repo/db/schema";
import { eq, and, or, desc } from "drizzle-orm";
import { TokenModule } from "@repo/lib";
import { getCookieOptions } from "@/lib/cookie-module.js";
import { bodyValidator } from "@/lib/validation-module.js";
import vine from "@vinejs/vine";
import { suggestUniqueUsername } from "../lib.js";
import { UserRepo } from "@repo/repo";

export class SSOAuthController {
  @bodyValidator(
    vine.object({
      idToken: vine.string().jwt(),
    }),
  )
  static async googleLogin(req: Request, res: Response, next: NextFunction) {
    try {
      const { idToken } = req.body;
      const { authType } = req.query;
      const payload = await GoogleAuthValidator.validateIdToken(idToken);
      if (!payload.email) {
        res.status(403).json({ message: "email not found" });
        return;
      }

      const [existingUser] = await db
        .select()
        .from(users)
        .where(eq(users.email, payload.email));

      if ((!existingUser || !existingUser.verified) && authType == "login") {
        res.status(404).json({ message: "User not found, Please sign up" });
        return;
      } else if (
        existingUser &&
        existingUser.verified &&
        authType == "signup"
      ) {
        res
          .status(409)
          .json({ message: "Account already exists, try logging in" });
        return;
      }

      const { accessToken, refreshToken, isSignupCompleted, user } =
        await ssoSignupLogin(payload.email);

      const suggestedUsername = await suggestUniqueUsername(payload.email);

      res.cookie(...getCookieOptions(refreshToken));
      res.json({
        data: {
          accessToken,
          refreshToken,
          user,
          email: payload.email,
          isSignupCompleted: isSignupCompleted,
          username: suggestedUsername,
        },
        message: "SSO successful",
        success: true,
      });
    } catch (error) {
      next(error);
    }
  }
  @bodyValidator(
    vine.object({
      idToken: vine.string().jwt(),
    }),
  )
  static async appleLogin(req: Request, res: Response, next: NextFunction) {
    try {
      const { idToken } = req.body;
      const { authType } = req.query;
      const payload = await AppleAuthValidator.validateIdToken(idToken);
      if (!payload.email) {
        res.status(409).json({ message: "email not found" });
        return;
      }
      const [existingUser] = await db
        .select()
        .from(users)
        .where(eq(users.email, payload.email));

      if ((!existingUser || !existingUser.verified) && authType == "login") {
        res.status(404).json({ message: "user not found" });
        return;
      } else if (
        existingUser &&
        existingUser.verified &&
        authType == "signup"
      ) {
        res.status(409).json({ message: "user already exists" });
        return;
      }
      const { accessToken, refreshToken, isSignupCompleted, user } =
        await ssoSignupLogin(payload.email);

      const suggestedUsername = await suggestUniqueUsername(payload.email);

      res.cookie(...getCookieOptions(refreshToken));
      res.json({
        data: {
          accessToken,
          refreshToken,
          user,
          email: payload.email,
          isSignupCompleted: isSignupCompleted,
          username: suggestedUsername,
        },
        message: "SSO successful",
        success: true,
      });
    } catch (error) {
      next(error);
    }
  }
}

async function ssoSignupLogin(email: string): Promise<{
  refreshToken: string;
  accessToken: string;
  user: any;
  isSignupCompleted: boolean;
}> {
  let user = await UserRepo.getPersonalUser({ email: email });

  if (!user) {
    await db.transaction(async (tx) => {
      tx.insert(users)
        .values({
          email: email,
          verifiedAt: new Date(),
          verified: true,
        })
        .returning();

      user = await UserRepo.getPersonalUser({ email: email }, tx)!;
    });
  }
  if (!user) {
    throw new Error("User not created");
  }
  if (!user.verifiedAt || !user.verified) {
    await db
      .update(users)
      .set({
        verifiedAt: new Date(),
        verified: true,
      })
      .where(eq(users.email, email));
  }

  const refreshToken = TokenModule.signRefreshToken({
    email: email,
    userID: user.id.toString(),
  });
  const accessToken = TokenModule.signAccessToken({
    email: email,
    userID: user.id.toString(),
  });

  await db
    .update(users)
    .set({
      refreshToken: refreshToken,
    })
    .where(eq(users.email, email));

  return {
    accessToken,
    refreshToken,
    user: !user.username ? null : user,
    isSignupCompleted: !user.username ? false : true,
  };
}
