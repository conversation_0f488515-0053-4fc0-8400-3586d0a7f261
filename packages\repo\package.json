{"name": "@repo/repo", "version": "0.0.0", "private": true, "type": "module", "exports": {".": {"production": {"import": "./dist/index.js"}, "default": "./src/index.ts"}}, "scripts": {"build": "tsc --project tsconfig.json && tsc-alias -p tsconfig.json", "lint": "eslint . --max-warnings 0"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/eslint": "^8.56.5", "@types/node": "^20.16.5", "@types/pg": "^8.11.10", "eslint": "^8.57.0", "tsc-alias": "^1.8.10", "typescript": "5.5.4"}, "dependencies": {"@repo/db": "workspace:^", "@repo/lib": "workspace:^", "dayjs": "^1.11.13", "drizzle-orm": "^0.36.0"}}