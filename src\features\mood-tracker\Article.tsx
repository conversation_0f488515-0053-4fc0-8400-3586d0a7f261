import {Text, View} from '@components/native';
import {useTheme} from '@context/index';
import {router} from '@navigation/refs/navigation-ref';
import React from 'react';
import {Image, ScrollView} from 'react-native';
import {ArticleData} from './Mood';

export const MoodArticle = () => {
  const {colors} = useTheme();
  const article = router.params('MoodArticle') as ArticleData;

  return (
    <ScrollView style={{flex: 1, backgroundColor: colors.background}}>
      <Image source={article.image} style={{width: '100%', height: 220, borderBottomLeftRadius: 16, borderBottomRightRadius: 16}} resizeMode="cover" />
      <View p={20}>
        <Text fs="22" fw="600" color="neutral80" mb={10}>
          {article.title}
        </Text>
        <Text fs="16" fw="400" color="neutral80" style={{lineHeight: 24}}>
          {article.content}
        </Text>
      </View>
    </ScrollView>
  );
};
