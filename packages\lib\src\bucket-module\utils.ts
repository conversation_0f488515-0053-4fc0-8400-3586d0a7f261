import { S3Bucket } from "./s3-storage.js";

export const pathGenerator = {
  asset: {
    base: "image/assets",
    supportImage: "image/assets/bemyguest_support.png"
  },
  profile: (userId: string) => `user/${userId}/profile`,
  posts: (userId: string) => `user/${userId}/posts`,
  journal: {
    images: (userId: string) => `user/${userId}/journal/images`,
    audio: (userId: string) => `user/${userId}/journal/audio`,
    categories: (userId: string) => `user/${userId}/journal/categories`
  },
  journalCategories: `default-journal-categories`
};

export const defaultAvatarUrl = `${S3Bucket.getAwsUrl("image/assets/avatar_image.png")}`;
