import vine from "@vinejs/vine";

export const createPostValidator = vine.object({
  body: vine.string(),
  title: vine.string(),
});

export const editPostValidator = vine.object({
  body: vine.string().optional(),
  title: vine.string().optional(),
  imageData: vine.array(
    vine.object({
      filename: vine.string(),
      type: vine.enum(["network", "file"]),
      index: vine.number(),
    }),
  ),
  oldImages: vine.array(vine.string()).optional(),
});
