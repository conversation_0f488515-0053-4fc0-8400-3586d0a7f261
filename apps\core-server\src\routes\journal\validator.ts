import { JournalChunkTypes } from "@repo/db/schema";
import vine from "@vinejs/vine";

export const journalValidator = vine.object({
  title: vine.string(),
  category: vine.string().uuid(),
  summary: vine.string(),
  description: vine.array(
    vine.object({
      type: vine.enum(JournalChunkTypes),
      value: vine.string(),
      fileName: vine.string().optional(),
    }),
  ),
});

export const updateJournalValidator = vine.object({
  title: vine.string(),
  category: vine.string().uuid(),
  summary: vine.string(),
  description: vine.array(
    vine.object({
      type: vine.enum(JournalChunkTypes),
      value: vine.string(),
      fileName: vine.string().optional(),
    }),
  ),
  mediaUpdated: vine.boolean().optional(),
});
