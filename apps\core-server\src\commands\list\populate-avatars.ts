import { Command } from "@/lib/command-module.js";
import db from "@repo/db";
import { avatarTable } from "@repo/db/schema";
const base = (count: number) =>
  `https://cdn.jsdelivr.net/gh/alohe/avatars/png/vibrent_${count}.png`;

export default new Command({
  name: "populate-avatars",
  description: "Populate avatars, that's it",
  fn: async () => {
    const images = Array.from({ length: 26 }, (_, i) => ({
      url: base(i + 1),
      name: `vibrant_${i + 1}`,
    }));

    await db.insert(avatarTable).values(images);
    console.log("Populated images from 1 to 27");
  },
});
