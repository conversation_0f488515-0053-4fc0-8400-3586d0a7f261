#!/bin/bash

echo "🔍 Patching html-entities for 'worklet' support..."

TARGET_FILES=(
  "node_modules/html-entities/dist/commonjs/index.js"
  "node_modules/html-entities/dist/esm/index.js"
)

for FILE in "${TARGET_FILES[@]}"; do
  if [ -f "$FILE" ]; then
    if grep -q "'worklet';" "$FILE"; then
      echo "✅ Already patched: $FILE"
    else
      echo "➕ Inserting 'worklet'; at the top of $FILE"
      tmpfile=$(mktemp)
      echo "'worklet';" > "$tmpfile"
      cat "$FILE" >> "$tmpfile"
      mv "$tmpfile" "$FILE"
      echo "✅ Patched: $FILE"
    fi
  else
    echo "❌ File not found: $FILE"
  fi
done
