name: Deploy Core-Server to ECS

on:
  push:
    branches:
      - staging

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: core-server-staging
  ECS_CLUSTER: ecs-cluster-staging
  ECS_SERVICE: staging-core-server-service
  ECS_TASK_DEFINITION: .aws/task-definitions/core-server-staging.json
  CONTAINER_NAME: core-server-staging

permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Verify identity
        run: aws sts get-caller-identity

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Build Docker image
        run: |
          IMAGE_TAG=${{ github.sha }}
          IMAGE_URI=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}
          docker build -f apps/core-server/Dockerfile \
            -t $IMAGE_URI:$IMAGE_TAG \
            -t $IMAGE_URI:latest .

      - name: Push Docker image to ECR
        run: |
          IMAGE_TAG=${{ github.sha }}
          IMAGE_URI=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}
          docker push $IMAGE_URI:$IMAGE_TAG
          docker push $IMAGE_URI:latest

      - name: Update ECS Task Definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.ECS_TASK_DEFINITION }}
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:${{ github.sha }}

      - name: Deploy to ECS
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          service: ${{ env.ECS_SERVICE }}
          cluster: ${{ env.ECS_CLUSTER }}
          task-definition: ${{ steps.task-def.outputs.task-definition }}
