// File: BeMyGuestOtpEmail.tsx

import {
  Html,
  Head,
  Body,
  Container,
  Text,
  Section,
  Img,
  Heading,
  Hr,
  Row,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

export default function BeMyGuestOtpEmail({
  otpCode = "123345",
}: {
  otpCode?: string;
}) {
  return (
    <Html>
      <Head />
      <Tailwind>
        <Body className="bg-[#f3f3f3] font-sans">
          <Container className="bg-white mx-auto p-8 rounded-md shadow-md max-w-lg">
            {/* Header with logo and title */}
            <table className="-ml-1">
              <tr>
                <th>
                  <img
                    src="https://bmg-dev-buk.s3.us-east-1.amazonaws.com/image/assets/bmg-transparent-logo.png"
                    alt=""
                    height={20}
                    width={20}
                  />
                </th>
                <th>
                  <Text className="text-lg font-semibold text-[#5b2be0] m-0">
                    BeMyGuest
                  </Text>
                </th>
              </tr>
            </table>

            {/* Greeting and instruction */}
            <Text className="text-base text-black mb-4">Hi there!</Text>
            <Text className="text-base text-black mb-6">
              We’ve received a request to log in to your BeMyGuest account with
              this email address. To verify and complete the process, please
              enter the following code on your device:
            </Text>

            {/* OTP Code */}
            <Section className="text-center my-6">
              <div className="inline-block bg-[#5b2be0] text-white font-bold text-2xl px-8 py-4 rounded-md tracking-wider">
                <table className="-ml-1 relative">
                  <tr>
                    <th className="relative right-1">{otpCode}</th>
                    <th>
                      <img
                        src="https://bmg-dev-buk.s3.us-east-1.amazonaws.com/image/assets/copy-btn.png"
                        alt=""
                        className="absolute top-[9px]"
                        height={20}
                        width={20}
                      />
                    </th>
                  </tr>
                </table>
              </div>
              <Text className="text-sm font-semibold text-black mt-4">
                This code will be valid for the next 10 minutes.
              </Text>
            </Section>

            {/* Warning and contact */}
            <Text className="text-sm text-gray-700 mb-4">
              Please do not share this code with anyone. If you did not request
              this login, please reach out to our support team at{" "}
              <a href="mailto:<EMAIL>" className="text-[#5b2be0]">
                <EMAIL>
              </a>{" "}
              for assistance.
            </Text>
            <Text className="text-sm text-gray-600 mb-4">
              Please note that this is an automated message from BeMyGuest, and
              replies to this email are not monitored.
            </Text>

            {/* Closing */}
            <Text className="text-sm text-black">
              Thank you!
              <br />
              Cheers to a happy wallet, <br />
              The BeMyGuest Team
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
}
