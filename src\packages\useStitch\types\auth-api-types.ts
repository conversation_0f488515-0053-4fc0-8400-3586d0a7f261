export interface SignupLoginMutationBody {
  email: string;
}

export interface VerifyOtpMutationBody {
  email: string;
  otp: string;
}

export interface AutoLoginMutationBody {
  refreshToken: string;
}

export type GenderEnum = 'male' | 'female' | 'other';

export type SessionUser = {
  // only user present when singup is completed
  id: string;
  username: string;
  avatar: string;
  location: string;
  bio: string;
  email: string;
  playlistLink: string;
  gender: GenderEnum;
  age: string;
  profilePercentage: number;
  showCurrentMoodOnProfile: boolean;
  totalPostCount: number;
  totalHealCount: number;
  totalFollowing: number;
  totalFollowers: number;
};
export interface SessionUserResponse {
  accessToken: string;
  refreshToken: string;
  email: string;
  isSignupCompleted: boolean;
  user: null | SessionUser;
  username: string;
}
