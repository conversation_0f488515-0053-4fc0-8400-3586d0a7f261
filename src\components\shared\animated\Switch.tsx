import { ThemeColorKeys } from '@/types/color-types';
import { useTheme } from '@context/index';
import React, { useEffect } from 'react';
import {
  Pressable,
  View,
  StyleSheet,
  StyleProp,
  ViewStyle,
} from 'react-native';
import Animated, {
  interpolate,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

export interface SwitchProps {
  isActive: boolean;
  onToggle: (updatedValue: boolean) => void;
  size?: number;
  enabledColor?: ThemeColorKeys;
  disabledColor?: ThemeColorKeys;
  thumbColorActive?: ThemeColorKeys;
  thumbColorInactive?: ThemeColorKeys;
  duration?: number;
  disabled?: boolean;
  trackStyle?: StyleProp<ViewStyle>;
  thumbStyle?: StyleProp<ViewStyle>;
}

export const Switch: React.FC<SwitchProps> = ({
  isActive,
  onToggle,
  size = 28,
  enabledColor = 'purple500',
  disabledColor = 'background',
  thumbColorActive = 'purple500',
  thumbColorInactive = 'background',
  duration = 200,
  disabled = false,
  trackStyle,
  thumbStyle,
}) => {
  const { colors } = useTheme();
  const trackColors = {
    on: colors[enabledColor ?? 'purple500'],
    off: colors[disabledColor ?? 'background'],
  };
  const height = useSharedValue(0);
  const width = useSharedValue(0);
  const animatedValue = useSharedValue(isActive ? 1 : 0);

  // Sync animatedValue with isActive
  useEffect(() => {
    animatedValue.value = withTiming(isActive ? 1 : 0, { duration });
  }, [isActive, duration]);

  const trackAnimatedStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      animatedValue.value,
      [0, 1],
      [trackColors.off, trackColors.on],
    );
    return {
      backgroundColor: color,
      borderRadius: height.value / 2,
      borderColor: colors.purple500,
      borderWidth: 1,
    };
  });

  const thumbAnimatedStyle = useAnimatedStyle(() => {
    const moveValue = interpolate(
      animatedValue.value,
      [0, 1],
      [0, width.value - (height.value+1)],
    );
    return {
      transform: [{ translateX: moveValue }],
      borderRadius: 100,
      backgroundColor: !isActive
        ? colors[thumbColorActive]
        : colors[thumbColorInactive] || '#FFFFFF',
    };
  });

  const handlePress = () => {
    if (!disabled) {
      onToggle(!isActive);
    }
  };

  return (
    <Pressable onPress={handlePress} disabled={disabled}>
      <Animated.View
        onLayout={e => {
          height.value = e.nativeEvent.layout.height;
          width.value = e.nativeEvent.layout.width;
        }}
        style={[
          switchStyles.track,
          {
            width: size * 2,
            height: size,
            padding: size * 0.1,
            opacity: disabled ? 0.5 : 1,
          },
          trackStyle,
          trackAnimatedStyle,
        ]}
      >
        <Animated.View
          style={[
            {
              height: size * 0.8,
              width: size * 0.8,
              backgroundColor: colors.background,
            },
            thumbStyle,
            thumbAnimatedStyle,
          ]}
        />
      </Animated.View>
    </Pressable>
  );
};

const switchStyles = StyleSheet.create({
  track: {
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
});
