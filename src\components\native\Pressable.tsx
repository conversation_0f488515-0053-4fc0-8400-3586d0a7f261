import {ThemeColorKeys} from '@/types/color-types';
import {useTheme} from '@context/Theme/ThemeContext';
import {forwardRef} from 'react';
import {DimensionValue, Pressable as RNPressable, PressableProps as RNPressableProps, ViewStyle} from 'react-native';
import Animated from 'react-native-reanimated';
import {moderateScale, moderateVerticalScale, scale, verticalScale} from 'react-native-size-matters';

export interface PressableProps extends RNPressableProps {
  bg?: ThemeColorKeys;
  w?: number | DimensionValue;
  mih?: number | DimensionValue;
  h?: number;
  br?: number;
  bw?: number;
  bbw?: number;
  bc?: ThemeColorKeys;
  p?: number;
  pt?: number;
  pb?: number;
  pl?: number;
  pr?: number;
  px?: number;
  py?: number;
  m?: number;
  mt?: number;
  mb?: number;
  ml?: number;
  mr?: number;
  mx?: number;
  my?: number;
  display?: 'flex' | 'contents' | 'none';
  flex?: number;
  fd?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  center?: boolean;
  jc?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  ai?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';
  fw?: 'wrap' | 'nowrap' | 'wrap-reverse';
  row?: boolean;
  column?: boolean;
  flexCenterRow?: boolean;
  flexCenterColumn?: boolean;
  gap?: number;
  left?: DimensionValue;
  right?: DimensionValue;
  top?: DimensionValue;
  bottom?: DimensionValue;
  pos?: 'relative' | 'absolute' | 'static' | 'sticky' | 'fixed';
  disableSizeMatter?: boolean;
  z?: number;
  size?: number;
}

export const Pressable = forwardRef<typeof RNPressable, PressableProps>(
  (
    {
      bg = 'transparent',
      w,
      h,
      br,
      bw,
      bc,
      mih,
      p,
      pt,
      pb,
      pl,
      pr,
      px,
      py,
      m,
      mt,
      mb,
      ml,
      mr,
      mx,
      my,
      flex,
      fd,
      center,
      jc,
      ai,
      fw,
      row,
      bbw,
      column,
      flexCenterRow,
      flexCenterColumn,
      gap,
      style,
      display,
      top,
      bottom,
      left,
      right,
      disableSizeMatter = false,
      z,
      size,
      pos,
      ...props
    },
    ref,
  ) => {
    const {colors} = useTheme();

    const defaultStyles = {
      backgroundColor: colors[bg],
      ...(w !== undefined && {
        width: disableSizeMatter ? w : !isNaN(Number(w)) ? scale(Number(w)) : w,
      }),
      ...(mih !== undefined && {
        minHeight: disableSizeMatter ? mih : !isNaN(Number(mih)) ? scale(Number(mih)) : mih,
      }),
      ...(h !== undefined && {
        height: disableSizeMatter ? h : verticalScale(h),
      }),
      ...(br !== undefined && {borderRadius: moderateScale(br, 0.5)}),
      ...(bw !== undefined && {borderWidth: moderateScale(bw, 0.5)}),
      ...(bbw !== undefined && {borderBottomWidth: moderateScale(bbw, 0.5)}),
      ...(bc !== undefined && {borderColor: colors[bc]}),
      ...(p !== undefined && {padding: moderateScale(p, 0.5)}),
      ...(pt !== undefined && {paddingTop: moderateScale(pt, 0.5)}),
      ...(pb !== undefined && {paddingBottom: moderateScale(pb, 0.5)}),
      ...(pl !== undefined && {paddingLeft: moderateScale(pl, 0.5)}),
      ...(pr !== undefined && {paddingRight: moderateScale(pr, 0.5)}),
      ...(px !== undefined && {paddingHorizontal: moderateScale(px, 0.5)}),
      ...(py !== undefined && {
        paddingVertical: moderateVerticalScale(py, 0.5),
      }),
      ...(m !== undefined && {margin: moderateScale(m, 0.5)}),
      ...(mt !== undefined && {marginTop: moderateScale(mt, 0.5)}),
      ...(mb !== undefined && {marginBottom: moderateScale(mb, 0.5)}),
      ...(ml !== undefined && {marginLeft: moderateScale(ml, 0.5)}),
      ...(mr !== undefined && {marginRight: moderateScale(mr, 0.5)}),
      ...(mx !== undefined && {marginHorizontal: moderateScale(mx, 0.5)}),
      ...(my !== undefined && {marginVertical: moderateVerticalScale(my, 0.5)}),
      ...(flex !== undefined && {flex}),
      ...(pos !== undefined && {position: pos}),
      ...(fd !== undefined && {flexDirection: fd}),
      ...(center && {alignItems: 'center', justifyContent: 'center'}),
      ...(jc !== undefined && {justifyContent: jc}),
      ...(ai !== undefined && {alignItems: ai}),
      ...(fw !== undefined && {flexWrap: fw}),
      ...(display != undefined && {display: display}),
      ...(row && {flexDirection: 'row'}),
      ...(column && {flexDirection: 'column'}),
      ...(left && {left}),
      ...(top && {top}),
      ...(bottom && {bottom}),
      ...(right && {right}),
      ...(z && {zIndex: z}),
      ...(flexCenterRow && {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }),
      ...(flexCenterColumn && {
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      }),
      ...(gap !== undefined && {gap: moderateScale(gap, 0.5)}),
      ...(size != undefined && {height: size, width: size}),
    } as ViewStyle;

    return (
      <RNPressable ref={ref as any} style={[defaultStyles, style as any]} {...props}>
        {props.children}
      </RNPressable>
    );
  },
);

Pressable.displayName = 'Pressable';

export const AnimatedPressable = Animated.createAnimatedComponent(Pressable);
AnimatedPressable.displayName = 'AnimatedPressable';
