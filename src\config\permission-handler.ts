import {PermissionsAndroid, Platform} from 'react-native';
import {PERMISSIONS, RESULTS, check, request} from 'react-native-permissions';

class PermissionsManager {
  /**
   * Requests permission for recording audio and accessing storage
   * on both Android and iOS platforms.
   *
   * On Android:
   * - RECORD_AUDIO
   * - READ_EXTERNAL_STORAGE
   * - WRITE_EXTERNAL_STORAGE
   *
   * On iOS:
   * - MICROPHONE
   *
   * @returns {Promise<boolean>} Whether all required permissions are granted.
   *
   * @example
   * const hasPermission = await Permission.requestStorageAndAudioPermission();
   * if (!hasPermission) {
   *   console.log('Permissions not granted');
   * }
   */
  async requestStorageAndAudioPermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const res = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
        ]);

        const allGranted =
          res[PermissionsAndroid.PERMISSIONS.RECORD_AUDIO] === PermissionsAndroid.RESULTS.GRANTED &&
          res[PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE] === PermissionsAndroid.RESULTS.GRANTED &&
          res[PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE] === PermissionsAndroid.RESULTS.GRANTED;

        return allGranted;
      } catch (err) {
        console.warn('Android permission error:', err);
        return false;
      }
    } else if (Platform.OS === 'ios') {
      try {
        const micStatus = await check(PERMISSIONS.IOS.MICROPHONE);
        if (micStatus === RESULTS.GRANTED) {
          return true;
        }

        const result = await request(PERMISSIONS.IOS.MICROPHONE);
        return result === RESULTS.GRANTED;
      } catch (err) {
        console.warn('iOS permission error:', err);
        return false;
      }
    }

    return false;
  }
}

export const Permission = new PermissionsManager();
