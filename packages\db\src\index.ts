import { drizzle, NodePgQueryResultHKT } from "drizzle-orm/node-postgres";
import * as schema from "./schema/index.js";
import Pg from "pg";
import { PgSelectBase, PgTransaction } from "drizzle-orm/pg-core";
import { ExtractTablesWithRelations } from "drizzle-orm";

export const client = new Pg.Pool({
  connectionString: process.env.DATABASE_URL,
});
const db = drizzle({ client: client, schema });
export const prodClient = new Pg.Pool({
  connectionString: process.env.PROD_DB_URL,
});
export const prodDb = drizzle({
  client: prodClient,
});
export type DrizzleTransaction = PgTransaction<
  NodePgQueryResultHKT,
  typeof schema,
  ExtractTablesWithRelations<typeof schema>
>;
export type DrizzleDB = typeof db;
export default db;

// extra sql wrappers
export * from "./functions.js";
export * from "./functions/index.js";
