import {IntroScreen} from '@features/auth';
import {createStackNavigator} from '@react-navigation/stack';
import SCREENS from '@src/SCREENS';
import React, {lazy, Suspense} from 'react';
import {publicScreenComponents} from './public-screens-components';

const unauthComponents = {
  [SCREENS.LOGIN]: lazy(() =>
    import('@features/auth/Login').then(module => ({
      default: module.LoginScreen,
    })),
  ),
  [SCREENS.SIGNUP]: lazy(() =>
    import('@features/auth/Signup').then(module => ({
      default: module.SignupScreen,
    })),
  ),
  [SCREENS.VERIFY_OTP]: lazy(() =>
    import('@features/auth/VerifyOtp').then(module => ({
      default: module.VerifyOtp,
    })),
  ),

  ...publicScreenComponents, // do not remove this
};
const Stack = createStackNavigator();

const UnAuthScreens = () => {
  return (
    <Suspense fallback={null}>
      <Stack.Navigator initialRouteName={SCREENS.INTRO} screenOptions={{headerShown: false}}>
        {/**
         * The initial screen (IntroScreen) is imported upfront to ensure fast rendering
         * and avoid React Navigation warnings or Suspense-related delays on app startup.
         * Other screens are lazy-loaded to reduce initial bundle size and improve performance.
         */}
        <Stack.Screen name={SCREENS.INTRO} component={IntroScreen} />
        {Object.entries(unauthComponents).map(([name, component]) => (
          <Stack.Screen key={name} name={name} component={component} />
        ))}
      </Stack.Navigator>
    </Suspense>
  );
};

export default UnAuthScreens;
