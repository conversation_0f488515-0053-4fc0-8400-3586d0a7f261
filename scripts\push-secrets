#!/bin/zsh


# reading from .env file

json=""
total=$(grep -E '^[A-Z][A-Z0-9_]*=' ./scripts/.env | wc -l | tr -d ' ')
index=1

while IFS='=' read -r key value; do
  [[ -z "$key" || "$key" =~ ^\s*# ]] && continue

  # Strip leading/trailing whitespace and quotes
  key=$(echo "$key" | xargs)
  value=$(echo "$value" | xargs)
  value="${value%\"}"; value="${value#\"}"
  value="${value%\'}"; value="${value#\'}"

  json+="\"$key\": \"$value\""

  if (( index != ((total - 1)) )); then
    json+=","
  fi

  (( index++ ))
done < ./scripts/.env

secrets="{$json}"

# pushing to aws

SECRET_NAME=env-staging
DESCRIPTION="Secrets for staging environment"
JSON_STRING=$secrets

if aws secretsmanager describe-secret --secret-id "$SECRET_NAME" &>/dev/null; then
  echo "🔄 Updating secret: $SECRET_NAME"
  aws secretsmanager put-secret-value \
    --secret-id "$SECRET_NAME" \
    --secret-string "$JSON_STRING"
else
  echo "➕ Creating secret: $SECRET_NAME"
  aws secretsmanager create-secret \
    --name "$SECRET_NAME" \
    --secret-string "$JSON_STRING" \
    --description "$DESCRIPTION"
fi