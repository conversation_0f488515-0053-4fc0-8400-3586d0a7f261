import {View} from '@components/native';
import {useTheme} from '@context/index';
import {useJournalStore} from '@features/journal/store/journal-store';
import React, {FC} from 'react';
import {ScrollView, TextInput} from 'react-native';
import {TokenImage, TokenTextInput} from './tokens';
import AudioInputToken from './tokens/AudioInputToken';

interface JournalLiveMarkdownEditorProps {}

const JournalLiveMarkdownEditor: FC<JournalLiveMarkdownEditorProps> = () => {
  const {inputValue, setInputValue, scrollViewRef, tokens, onKeyPress, defaultInputRef, goToNextToken, removeToken, updateToken, setCurrentFocusedIndex} = useJournalStore();
  const {colors} = useTheme();

  return (
    <View style={{flex: 1}} bg="neutral00" br={20}>
      <ScrollView showsVerticalScrollIndicator={false} ref={scrollViewRef} style={{flex: 1, borderRadius: 20, padding: 20}}>
        <View style={{flex: 1}} bg="neutral00">
          {tokens.map((token, index) => {
            switch (token.type) {
              case 'text-input':
                return (
                  <TokenTextInput
                    key={index}
                    index={index}
                    ref={tokens[index].ref}
                    value={token?.value?.toString() ?? ''}
                    removeMe={removeToken}
                    updateToken={updateToken}
                    goToNextToken={goToNextToken}
                  />
                );
              case 'image-file':
                return <TokenImage index={index} key={index} removeMe={removeToken} value={token?.value?.toString() ?? ''} />;
              case 'audio-file':
                return <AudioInputToken index={index} key={index} removeMe={removeToken} value={token?.value?.toString() ?? ''} />;
              default:
                return <React.Fragment key={index}></React.Fragment>;
            }
          })}
          <TextInput
            placeholder="Write your journal..."
            onChangeText={setInputValue}
            onKeyPress={onKeyPress}
            ref={defaultInputRef}
            onFocus={e => setCurrentFocusedIndex(-1)}
            onPress={e => setCurrentFocusedIndex(-1)}
            value={inputValue}
            placeholderTextColor={colors.neutral40}
            multiline
            scrollEnabled={false}
            style={{
              fontSize: 16,
              color: colors.neutral80,
              backgroundColor: 'transparent',
              flex: 1,
              marginBottom: 20,
            }}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default JournalLiveMarkdownEditor;
