// packages/useStitch/apis/profile-apis.ts
import {ApiResponse, MutationApiConfig} from '../types/common-api-types';

export namespace JournalApis {
  export const createJournal = 'createJournal' as const;
}

export const journalApiConfig = {
  createJournal: {
    path: '/journal/add',
    method: 'POST',
    protected: false,
    mutationBody: undefined as unknown as any,
    responseType: undefined as unknown as ApiResponse<any>,
    baseCacheKey: 'add-journal',
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<any, any>,
};
