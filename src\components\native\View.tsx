import {ThemeColorKeys} from '@/types/color-types';
import {useTheme} from '@context/Theme/ThemeContext';

import {forwardRef} from 'react';
import {DimensionValue, View as RNView, ViewProps as RNViewProps, ViewStyle} from 'react-native';
import Animated from 'react-native-reanimated';
import {moderateScale, moderateVerticalScale, scale, verticalScale} from 'react-native-size-matters';

export interface ViewProps extends RNViewProps {
  /**
   * Background color enum
   */
  bg?: ThemeColorKeys;
  /**
   * Width
   */
  w?: number | DimensionValue;
  /**
   * Min Width
   */
  miw?: number | DimensionValue;
  /**
   * Min height
   */
  mih?: number | DimensionValue;
  /**
   * Max Width
   */
  maw?: number | DimensionValue;
  /**
   * Max height
   */
  mah?: number | DimensionValue;
  /**
   * Height
   */
  h?: number;
  /**
   * Border radius
   */
  br?: number;
  /**
   * Border Top Left radius
   */
  btl?: number;
  /**
   * Border Top Right radius
   */
  btr?: number;
  /**
   * Border Bottom Right radius
   */
  bbr?: number;
  /**
   * Border Bottom Left radius
   */
  bbl?: number;
  /**
   * Border width
   */
  bw?: number;
  /**
   * Border color enum
   */
  bc?: ThemeColorKeys;
  /**
   * Padding
   */
  p?: number;
  /**
   * Padding top
   */
  pt?: number;
  /**
   * Padding bottom
   */
  pb?: number;
  /**
   * Padding left
   */
  pl?: number;
  /**
   * Padding right
   */
  pr?: number;
  /**
   * Padding horizontal
   */
  px?: number;
  /**
   * Padding vertical
   */
  py?: number;
  /**
   * Margin
   */
  m?: number;
  /**
   * Margin top
   */
  mt?: number;
  /**
   * Margin bottom
   */
  mb?: number;
  /**
   * Margin left
   */
  ml?: number;
  /**
   * Margin right
   */
  mr?: number;
  /**
   * Margin horizontal
   */
  mx?: number;
  /**
   * Margin vertical
   */
  my?: number;
  /**
   * Dispaly
   */
  display?: 'flex' | 'contents' | 'none';
  /**
   * Flex
   */
  flex?: number;
  /**
   * Flex direction
   */
  fd?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  /**
   * Center content (sets alignItems and justifyContent to center)
   */
  center?: boolean;
  /**
   * Justify content
   */
  jc?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  /**
   * Align items
   */
  ai?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';
  /**
   * Flex wrap
   */
  fw?: 'wrap' | 'nowrap' | 'wrap-reverse';
  /**
   * Shorthand for flexDirection: row
   */
  row?: boolean;
  /**
   * Shorthand for flexDirection: column
   */
  column?: boolean;
  /**
   * Shorthand for flexDirection: row, alignItems: center, justifyContent: center
   */
  flexCenterRow?: boolean;
  /**
   * Shorthand for flexDirection: column, alignItems: center, justifyContent: center
   */
  flexCenterColumn?: boolean;
  /**
   * Gap between flex items
   */
  gap?: number;

  left?: DimensionValue;
  right?: DimensionValue;
  top?: DimensionValue;
  bottom?: DimensionValue;

  /**
   * Position
   */
  pos?: 'relative' | 'absolute' | 'static';

  /**
   * Disables the use of react native size matters completely. Default is false
   */
  disableSizeMatter?: boolean;

  /**
   * Z index
   */
  z?: number;

  /**
   * Size takes the size and create a component with same height and width , without size matter extension
   */
  size?: number;

  /**
   * Optional shadow default is null
   */
  shadow?: 'sm' | 'md' | 'lg' | 'xl';

  shadowC?: ThemeColorKeys;

  flexGrow?: number;
  flexShrink?: number;
}

export const View = forwardRef<RNView, ViewProps>(
  (
    {
      bg = 'transparent',
      w,
      h,
      br,
      btl,
      btr,
      shadow,
      mah,
      maw,
      shadowC = 'foreground',
      bbl,
      flexGrow,
      flexShrink,
      bbr,
      bw,
      bc,
      miw,
      mih,
      p,
      pt,
      pb,
      pl,
      pr,
      pos,
      px,
      py,
      m,
      mt,
      mb,
      ml,
      mr,
      mx,
      my,
      flex,
      fd,
      center,
      jc,
      ai,
      fw,
      row,
      column,
      flexCenterRow,
      flexCenterColumn,
      gap,
      style,
      display,
      top,
      bottom,
      left,
      right,
      disableSizeMatter = false,
      z,
      size,
      ...props
    },
    ref,
  ) => {
    const {colors} = useTheme();

    const defaultStyles = {
      backgroundColor: colors[bg],
      ...(w !== undefined && {
        width: disableSizeMatter ? w : !isNaN(Number(w)) ? scale(Number(w)) : w,
      }),
      ...(mih !== undefined && {
        minHeight: disableSizeMatter ? mih : !isNaN(Number(mih)) ? scale(Number(mih)) : mih,
      }),
      ...(miw !== undefined && {
        minWidth: disableSizeMatter ? miw : !isNaN(Number(miw)) ? scale(Number(miw)) : miw,
      }),
      ...(mah !== undefined && {
        maxHeight: disableSizeMatter ? mah : !isNaN(Number(mah)) ? scale(Number(mah)) : mah,
      }),
      ...(maw !== undefined && {
        maxWidth: disableSizeMatter ? maw : !isNaN(Number(maw)) ? scale(Number(maw)) : maw,
      }),
      ...(h !== undefined && {
        height: disableSizeMatter ? h : verticalScale(h),
      }),
      ...(br !== undefined && {borderRadius: br}),
      ...(flexGrow !== undefined && {flexGrow: flexGrow}),
      ...(flexShrink !== undefined && {flexShrink: flexShrink}),
      ...(btl !== undefined && {borderTopLeftRadius: btl}),
      ...(btr !== undefined && {borderTopRightRadius: btr}),
      ...(bbl !== undefined && {borderBottomLeftRadius: bbl}),
      ...(bbr !== undefined && {borderBottomRightRadius: bbr}),
      ...(bw !== undefined && {borderWidth: bw}),
      ...(bc !== undefined && {borderColor: colors[bc]}),
      ...(p !== undefined && {padding: moderateScale(p, 0.5)}),
      ...(pt !== undefined && {paddingTop: moderateScale(pt, 0.5)}),
      ...(pb !== undefined && {paddingBottom: moderateScale(pb, 0.5)}),
      ...(pl !== undefined && {paddingLeft: moderateScale(pl, 0.5)}),
      ...(pr !== undefined && {paddingRight: moderateScale(pr, 0.5)}),
      ...(px !== undefined && {paddingHorizontal: moderateScale(px, 0.5)}),
      ...(py !== undefined && {
        paddingVertical: moderateVerticalScale(py, 0.5),
      }),
      ...(m !== undefined && {margin: moderateScale(m, 0.5)}),
      ...(mt !== undefined && {marginTop: moderateScale(mt, 0.5)}),
      ...(mb !== undefined && {marginBottom: moderateScale(mb, 0.5)}),
      ...(ml !== undefined && {marginLeft: moderateScale(ml, 0.5)}),
      ...(mr !== undefined && {marginRight: moderateScale(mr, 0.5)}),
      ...(mx !== undefined && {marginHorizontal: moderateScale(mx, 0.5)}),
      ...(my !== undefined && {marginVertical: moderateVerticalScale(my, 0.5)}),
      ...(flex !== undefined && {flex}),
      ...(pos !== undefined && {position: pos}),
      ...(fd !== undefined && {flexDirection: fd}),
      ...(center && {alignItems: 'center', justifyContent: 'center'}),
      ...(jc !== undefined && {justifyContent: jc}),
      ...(ai !== undefined && {alignItems: ai}),
      ...(fw !== undefined && {flexWrap: fw}),
      ...(display != undefined && {display: display}),
      ...(row && {flexDirection: 'row'}),
      ...(column && {flexDirection: 'column'}),
      ...(left != undefined && {left}),
      ...(top != undefined && {top}),
      ...(bottom != undefined && {bottom}),
      ...(right != undefined && {right}),
      ...(z && {zIndex: z}),
      ...(flexCenterRow && {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }),
      ...(flexCenterColumn && {
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      }),
      ...(gap !== undefined && {gap: moderateScale(gap, 0.5)}),
      ...(size != undefined && {height: size, width: size}),
      ...(shadow && {
        shadowColor: colors[shadowC],
        shadowOffset: {
          width: 0,
          height: shadow === 'sm' ? 2 : shadow === 'md' ? 4 : shadow === 'lg' ? 6 : shadow === 'xl' ? 8 : 0,
        },
        shadowOpacity: shadow === 'sm' ? 0.05 : shadow === 'md' ? 0.08 : shadow === 'lg' ? 0.15 : shadow === 'xl' ? 0.4 : 0,
        shadowRadius: shadow === 'sm' ? 2 : shadow === 'md' ? 4 : shadow === 'lg' ? 6 : shadow === 'xl' ? 8 : 0,
        elevation: shadow === 'sm' ? 2 : shadow === 'md' ? 4 : shadow === 'lg' ? 6 : shadow === 'xl' ? 8 : 0,
      }),
    } as ViewStyle;

    return (
      <RNView ref={ref} style={[defaultStyles, style]} {...props}>
        {props.children}
      </RNView>
    );
  },
);

View.displayName = 'View';

export const AnimatedView = Animated.createAnimatedComponent(View);

AnimatedView.displayName = 'AnimatedView';
