{"extends": "@repo/typescript-config/base.json", "compilerOptions": {"strict": true, "strictNullChecks": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "declarationMap": false, "noErrorTruncation": true, "declaration": false, "outDir": "./dist", "baseUrl": ".", "jsx": "react-jsx", "paths": {"@/*": ["./src/*"], "react": ["node_modules/@types/react"]}, "types": ["express", "mocha"], "typeRoots": ["./node_modules/@types", "./types"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "tests/**/*.ts", "tests/**/*.d.ts"]}