import {<PERSON><PERSON>View} from 'moti';
import React from 'react';
import {ViewStyle} from 'react-native';

interface StaggeredMotiFadeInPropsAnim {
  children: React.ReactNode | React.ReactNode[]; // can be one or many
  /**
   * Default to 150
   */
  delayPerItem?: number;
  duration?: number;
  initialOffsetY?: number;
  containerStyle?: ViewStyle;
}

export const StaggeredMotiFadeInAnim: React.FC<StaggeredMotiFadeInPropsAnim> = ({children, delayPerItem = 150, duration = 500, initialOffsetY = 20, containerStyle}) => {
  const childrenArray = React.Children.toArray(children); // normalize single/multiple children

  return (
    <>
      {childrenArray.map((child, index) => (
        <MotiView
          key={index}
          from={{opacity: 0, translateY: initialOffsetY}}
          animate={{opacity: 1, translateY: 0}}
          transition={{
            type: 'timing',
            duration,
            delay: index * delayPerItem,
          }}
          style={containerStyle}>
          {child}
        </MotiView>
      ))}
    </>
  );
};
