import { Button, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { BounceTap } from '@components/shared/animated';
import React, { useState } from 'react';
import { Dimensions } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { FlatGrid } from 'react-native-super-grid';
import { MotiView } from 'moti';

const { width } = Dimensions.get('screen');

export const SelectAvatarScreen = () => {
  const [selected, setSelected] = useState<number | undefined>();
  const { bottom } = useSafeAreaInsets();
  const BOX_SIZE = width / 3 - 5;
  const AVATAR_BOX_WIDTH = BOX_SIZE > 70 ? 70 : BOX_SIZE;

  // Generate random delays and durations for each card
  const getRandomDelay = () => Math.random() * 800;
  const getRandomDuration = () => 400 + Math.random() * 400;

  return (
    <ScreenWrapper withTouchableFeedback={false} title="Select your avatar">
      <FlatGrid
        itemDimension={BOX_SIZE}
        maxItemsPerRow={3}
        contentContainerStyle={{ paddingBottom: BOX_SIZE }}
        spacing={1}
        data={Array.from({ length: 18 }, (_, index) => ({
          id: index,
          item: 'avatar',
        }))}
        renderItem={({ item, index }) => (
          <View h={BOX_SIZE} flexCenterRow disableSizeMatter key={index}>
            <BounceTap
              onPress={() => {
                setSelected(prev => (prev === index ? undefined : index));
              }}
            >
              <MotiView
                from={{
                  opacity: 0,
                  scale: 1.5,
                }}
                animate={{
                  opacity: 1,
                  scale: 1,
                }}
                transition={{
                  type: 'timing',
                  duration: getRandomDuration(),
                  delay: getRandomDelay(),
                }}
              >
                <View bw={selected === index ? 8 : 0} bc="purpleLight" br={100}>
                  <View
                    bc={selected === index ? 'purple500' : 'neutral10'}
                    bw={2}
                    br={100}
                    size={AVATAR_BOX_WIDTH}
                    bg="lightBlue"
                  ></View>
                </View>
              </MotiView>
            </BounceTap>
          </View>
        )}
        // Add paddingBottom to account for the absolute button
        style={{ paddingBottom: bottom + BOX_SIZE }}
      />
      <View
        bg="background"
        px={20}
        py={10}
        pos="absolute"
        bottom={0}
        left={0}
        right={0}
        pb={bottom}
      >
        <Button h={46} onPress={() => {}}>
          UPDATE
        </Button>
      </View>
    </ScreenWrapper>
  );
};
