import {View} from '@components/native';
import {BounceTap} from '@components/shared/animated';
import {TypeEnum} from '@features/journal/store/journal-store';
import {forwardRef} from 'react';
import {Image, ImageProps} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

export interface TokenImageProps extends ImageProps {
  value: string;
  index: number;
  removeMe?: (myIndex: number, type: TypeEnum) => void;
}

export const TokenImage = forwardRef<Image, TokenImageProps>((props, ref) => {
  const {value, removeMe, index, ...rest} = props;

  return (
    <View style={{position: 'relative'}}>
      <View
        style={{
          width: 200,
          height: 200,
          marginVertical: 10,
        }}>
        <Image
          source={{uri: value}}
          style={{
            minHeight: 200,
            maxHeight: 450,
            minWidth: 150,
            maxWidth: 500,
            borderRadius: 8,
          }}
          {...rest}
        />
        <View pos="absolute" top={10} right={10}>
          <BounceTap onPress={() => removeMe?.(index, 'image-file')}>
            <View br={100} size={25} bg="neutral100" flexCenterRow>
              <Ionicons size={22} name="close" color="white" />
            </View>
          </BounceTap>
        </View>
      </View>
    </View>
  );
});
