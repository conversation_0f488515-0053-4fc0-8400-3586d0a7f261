//

import db from "@repo/db";
import { ChatMessage } from "./types.js";
import { chatMessageTable } from "@repo/db/schema";

interface BackupChat {
  takeBackup(message: ChatMessage, roomId: string): Promise<void>;
}
export class DirectChatBackup implements BackupChat {
  async takeBackup(message: ChatMessage, roomId: string) {
    await db.insert(chatMessageTable).values([
      {
        id: message.id,
        message: message.msg,
        roomId,
        senderId: message.userId,
        sentAt: message.st,
        attachments: message.ats,
        linkPreview: message.lp,
      },
    ]);
  }
}
export class CacheChatBackup implements BackupChat {
  async takeBackup(message: ChatMessage, roomId: string) {
    throw new Error("Method not implemented.");
  }
}
