import "@repo/lib/env";
import { ServerEvents } from "./events/index.js";
import "@/config/custom-type.js";
import app from "./routes/server.js";
import { logger, PushNotificationManager } from "@repo/lib";

// PushNotificationManager.initialize({
//   type: "env",
// });

app.listen(process.env.PORT || 3000, () => {
  logger.info(`Server started on port ${process.env.PORT || 3000}`);
  ServerEvents.emitServerReady();
});
