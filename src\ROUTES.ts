// /src/ROUTES.ts !! using it for deep linking support later

const PUBLIC_ROUTES = {
  FAQS: 'faqs',
  PRIVACY_POLICY: 'privacy-policy',
  TERMS_OF_SERVICE: 'terms-of-service',
} as const;

const NON_AUTHENTICATED_ROUTES = {
  INTRO: 'intro',
  PRE_AUTH: 'pre-auth',
  SIGNUP: 'signup',
  LOGIN: 'login',
  VERIFY_OTP: {
    route: 'verify-otp',
    getRoute: (email: string, type: 'login' | 'signup') => {
      return `verify-otp?email=${email}&type=${type}`;
    },
  },
} as const;

const AUTHENTICATED_ROUTES = {
  GET_STARTED: 'get-started',
  ONBOARDING: 'onboarding',
  CONVERSATIONS: 'conversations', // 0th index bottom tab route which gives list of users i need to chat
  CHAT: {
    route: 'conversations/chat',
    getRoute: (firstName: string, userId: string, conversationId?: string) => {
      return `conversations/chat?firstName=${firstName}&userId=${userId}&conversationId=${conversationId}`;
    },
  },
  FEED: 'feed', // 1st index bttom tab route
  CREATE_NEW_POST: 'feed/post/create',
  CREATE_NEW_POLL: 'feed/poll/create',
  EDIT_POST: {
    route: 'feed/post/edit',
    getRoute: (postId: string) => {
      return `feed/post/edit?postId=${postId}`;
    },
  },
  EDIT_POLL: {
    route: 'feed/poll/edit',
    getRoute: (postId: string) => {
      return `feed/poll/edit?postId=${postId}`;
    },
  },
  JOURNAL: 'journal', // 2nd index bttom tab route
  JOURNAL_SEARCH: 'journal/search',
  ADD_JOURNAL: 'journal/add',
  EDIT_JOURNAL: {
    route: 'journal/edit',
    getRoute: (journalId: string) => {
      return `journal/edit?id=${journalId}`;
    },
  },
  MOOD_TRACKER: 'mood-tracker', // 3rd index bttom tab route
  MOOD_CALENDAR: 'mood-tracker/calendar',
  MOOD_ARTICLE: 'mood-tracker/article',
  PROFILE: 'profile', // 4rth index bttom tab route
  EDIT_PROFILE: 'profile/edit', // no need of sendin user id as my profile edit
  SELECT_AVATAR: 'profile/edit/avatars',
  USERS_PROFILE: {
    route: 'profile/user', // not my user profile other users profule screen
    getRoute: (userId: string) => {
      //
      return `profile/user?userId=${userId}`;
    },
  },
  // Now below are the side drawer screens
  BOOKMARKS: 'bookmarks',
  BLOCKED_CONTACTS: 'users/blocked',
  SETTINGS: 'settings',
} as const;

const ROUTES = {
  ...PUBLIC_ROUTES,
  ...NON_AUTHENTICATED_ROUTES,
  ...AUTHENTICATED_ROUTES,
} as const;

export default ROUTES;

type ExtractRouteName<TRoute> = TRoute extends {
  getRoute: (...args: any[]) => infer TRouteName;
}
  ? TRouteName
  : TRoute;

/**
 * Represents all routes in the app as a union of literal strings.
 */
type Route = {
  [K in keyof typeof ROUTES]: ExtractRouteName<(typeof ROUTES)[K]>;
}[keyof typeof ROUTES];
