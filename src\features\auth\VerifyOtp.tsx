import {Button, OtpInput, Text, View} from '@components/native';
import {StackBackButton} from '@components/shared';
import {useAuth} from '@context/index';
import {router} from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import React, {useState} from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import IoniIcons from 'react-native-vector-icons/Ionicons';
import AnimatedAuthPipeAnimationWrapper from './components/AnimatedAuthPipeAnimationWrapper';

export const VerifyOtp = () => {
  const params = router.params('VerifyOtp');
  const {handleVerifyOtpSuccess} = useAuth();
  const [otp, setOtp] = useState('');
  const {top} = useSafeAreaInsets();
  const {isMutating, mutateAsync} = useStitch('verifyOtp', {
    mutationOptions: {
      onSuccess: handleVerifyOtpSuccess,
    },
  });

  return (
    <AnimatedAuthPipeAnimationWrapper>
      <View flex={1} p={20}>
        {router.canGoBack() && <StackBackButton title="Enter authentication code" />}
        <View mt={top + 50} display="flex" fd="column" w="100%">
          <View display="flex" fd="column">
            <Text fw="500" color="neutral80">
              A 4-digit code was sent to
            </Text>
            <Text
              fw="400"
              color="purple700"
              style={{
                flexWrap: 'wrap',
                width: '100%',
              }}>
              {params?.email}
            </Text>
          </View>

          <Text
            onPress={router.back}
            fw="400"
            color="purple700"
            style={{
              alignSelf: 'flex-end',
            }}>
            Change email
          </Text>
        </View>
        <View mt={40}>
          <OtpInput onOtpChange={otp => setOtp(otp)} />
          <Text onPress={() => {}} ta="center" fw="600" color="neutral80" my={14}>
            Resend Code
          </Text>
          <Button
            isLoading={isMutating}
            onPress={() => {
              mutateAsync({
                email: params?.email!,
                otp,
              });
            }}
            mt={50}
            h={48}
            rightIcon={<IoniIcons name="arrow-forward" color="white" size={20} />}>
            Continue
          </Button>
        </View>
      </View>
    </AnimatedAuthPipeAnimationWrapper>
  );
};
