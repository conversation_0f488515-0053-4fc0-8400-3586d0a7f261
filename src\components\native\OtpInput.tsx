import {useTheme} from '@context/index';
import React, {useRef, useState} from 'react';
import {Keyboard, NativeSyntheticEvent, Platform, TextInput as RNTextInput, TextInputProps as RNTextInputProps, StyleSheet, View} from 'react-native';
import Animated, {useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';

export interface OtpInputProps extends RNTextInputProps {
  otpLength?: number;
  gapBottom?: number;
  gap?: number; // New prop for separator width
  onOtpComplete?: (otp: string) => void;
  onOtpChange?: (otp: string) => void;
  style?: any; // need to chage this in future.
}

export const OtpInput: React.FC<OtpInputProps> = ({
  otpLength = 4,
  gapBottom = 12,
  /// Default gap of 12
  gap = 12,
  onOtpComplete,
  onOtpChange,
  style,
  ...props
}) => {
  const [otp, setOtp] = useState<string[]>(Array(otpLength).fill(''));
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const inputRefs = useRef<(RNTextInput | null)[]>(Array(otpLength).fill(null));
  const {colors} = useTheme();

  const handleChange = (text: string, index: number) => {
    if (!/^[0-9]*$/.test(text) && text !== '') return; // Allow only digits or empty string

    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);
    onOtpChange?.(newOtp.join(''));
    if (text && index < otpLength - 1) {
      inputRefs.current[index + 1]?.focus();
      setFocusedIndex(index + 1);
    } else if (text && index === otpLength - 1) {
      Keyboard.dismiss();
      setFocusedIndex(-1);
      if (newOtp.every(digit => digit !== '')) {
        onOtpComplete?.(newOtp.join(''));
      }
    }
  };

  const handleKeyPress = (e: NativeSyntheticEvent<any>, index: number) => {
    if (e.nativeEvent.key === 'Backspace' && otp[index] === '' && index > 0) {
      const newOtp = [...otp];
      newOtp[index - 1] = '';
      setOtp(newOtp);
      inputRefs.current[index - 1]?.focus();
      setFocusedIndex(index - 1);
    }
  };

  const handleFocus = (index: number) => {
    setFocusedIndex(index);
  };

  const handleBlur = () => {
    setFocusedIndex(-1);
  };

  const getInputStyles = (index: number) => {
    const isFocused = focusedIndex === index;
    const animatedBorderColor = useSharedValue(isFocused ? colors.purple500 : colors.purpleLight);

    animatedBorderColor.set(
      withTiming(isFocused ? colors.purple500 : colors.purpleLight, {
        duration: 200,
      }),
    );

    return useAnimatedStyle(() => ({
      borderColor: animatedBorderColor.get(),
      backgroundColor: 'transparent',
    }));
  };

  return (
    <View style={[styles.container, {marginBottom: gapBottom}, style]}>
      {Array.from({length: otpLength}).map((_, index) => (
        <React.Fragment key={index}>
          <View style={styles.inputWrapper}>
            <Animated.View style={[styles.inputContainer, getInputStyles(index)]}>
              <RNTextInput
                {...props}
                ref={(ref: any) => (inputRefs.current[index] = ref)}
                value={otp[index]}
                maxLength={1}
                keyboardType="numeric"
                onChangeText={text => handleChange(text, index)}
                onKeyPress={e => handleKeyPress(e, index)}
                onFocus={() => handleFocus(index)}
                onBlur={handleBlur}
                style={[styles.input, {color: colors.neutral80}]}
                selectTextOnFocus
                autoFocus={index === 0 && Platform.OS !== 'android'}
              />
            </Animated.View>
          </View>
          {index < otpLength - 1 && <View style={[styles.separator, {width: gap}]} />}
        </React.Fragment>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputWrapper: {
    height: 50,
    width: 50,
  },
  inputContainer: {
    borderRadius: 10,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  input: {
    width: '100%',
    height: '100%',
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '700',
  },
  separator: {
    height: '100%',
  },
});
