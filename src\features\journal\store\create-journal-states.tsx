// import {createRef} from 'react';
// import {NativeSyntheticEvent, ScrollView, TextInput, TextInputKeyPressEventData} from 'react-native';
// import {Asset, launchImageLibrary} from 'react-native-image-picker';
// import {create} from 'zustand';

// // Type definitions
// export type TypeEnum = 'text-input' | 'image-file' | 'image-url' | 'audio-file' | 'audio-url';
// export type ValueEnum = string | number;

// export type Token = {
//   value: ValueEnum;
//   type: TypeEnum;
//   ref?: React.RefObject<any>;
//   tokens?: Token[];
//   fileName?: string;
//   asset?: Asset;
// };

// export type Tokens = Token[];

// // Interface for counter state
// interface CounterState {
//   charCount: number;
//   imageCount: number;
//   audioCount: number;
// }

// // Interface for the store's state and actions
// interface JournalStore {
//   currentFocusedIndex: number;
//   setCurrentFocusedIndex: (index: number) => void;
//   inputValue: string;
//   setInputValue: (value: string) => void;
//   title: string;
//   setTitle: (title: string) => void;
//   tokens: Tokens;
//   setTokens: (tokens: Tokens) => void;
//   getUpdatedInputsAsync: () => Promise<Tokens>;
//   onKeyPress: (e: NativeSyntheticEvent<TextInputKeyPressEventData>) => Promise<void>;
//   removeToken: (index: number, type: TypeEnum) => void;
//   updateToken: (index: number, value: string) => void;
//   handleImagePick: () => Promise<void>;
//   goToNextToken: (currentIndex: number) => void;
//   defaultInputRef: React.RefObject<TextInput | null>;
//   scrollViewRef: React.RefObject<ScrollView | null>;
//   counter: CounterState;
//   chosenJournalTypeId: string;
//   setChosenJournalTypeId: (id: string) => void;
//   updateCounterFromTokens: (tokensToCount: Tokens) => void;
// }

// // Constants
// const EVENT_KEYS = {
//   enter: 'Enter',
//   backspace: 'Backspace',
// };

// /**
//  * Zustand store for managing journal creation/editing state.
//  * Handles tokens (text, images, audio), input value, title, journal type, and counters.
//  */
// export const useJournalStore = create<JournalStore>((set, get) => ({
//   // State
//   currentFocusedIndex: -1,
//   inputValue: '',
//   title: '',
//   tokens: [],
//   counter: {
//     charCount: 0,
//     imageCount: 0,
//     audioCount: 0,
//   },
//   chosenJournalTypeId: '',
//   defaultInputRef: createRef<TextInput>(),
//   scrollViewRef: createRef<ScrollView>(),

//   // Actions
//   /**
//    * Sets the index of the currently focused token.
//    * @param index The index to set.
//    */
//   setCurrentFocusedIndex: index => set({currentFocusedIndex: index}),

//   /**
//    * Sets the current input value.
//    * @param value The input value to set.
//    */
//   setInputValue: value => set({inputValue: value}),

//   /**
//    * Sets the journal title.
//    * @param title The title to set.
//    */
//   setTitle: title => set({title}),

//   /**
//    * Sets the tokens array.
//    * @param tokens The new tokens array.
//    */
//   setTokens: tokens => set({tokens}),

//   /**
//    * Sets the chosen journal type ID.
//    * @param id The journal type ID to set.
//    */
//   setChosenJournalTypeId: id => set({chosenJournalTypeId: id}),

//   /**
//    * Adds a new text input token and clears the input value.
//    * @returns A promise resolving to the updated tokens array.
//    */
//   getUpdatedInputsAsync: async () => {
//     const {tokens, inputValue, setInputValue, setTokens, updateCounterFromTokens} = get();
//     return new Promise(resolve => {
//       const newToken: Token = {
//         type: 'text-input',
//         value: inputValue,
//         ref: createRef<TextInput>(),
//       };
//       const updatedTokens: Tokens = [...tokens, newToken];
//       setTokens(updatedTokens);
//       setInputValue('');
//       updateCounterFromTokens(updatedTokens);
//       resolve(updatedTokens);
//     });
//   },

//   /**
//    * Handles key press events for text inputs (Enter and Backspace).
//    * @param e The key press event.
//    */
//   onKeyPress: async (e: NativeSyntheticEvent<TextInputKeyPressEventData>) => {
//     const {currentFocusedIndex, inputValue, tokens, getUpdatedInputsAsync, updateCounterFromTokens} = get();
//     if (e.nativeEvent.key === EVENT_KEYS.backspace) {
//       if (currentFocusedIndex === -1 && inputValue === '') {
//         if (tokens.length > 0 && tokens[tokens.length - 1].type === 'text-input') {
//           tokens[tokens.length - 1].ref?.current?.focus();
//         }
//       }
//     }
//     if (e.nativeEvent.key === EVENT_KEYS.enter) {
//       const updatedTokens = await getUpdatedInputsAsync();
//       updateCounterFromTokens(updatedTokens);
//     }
//   },

//   /**
//    * Removes a token at the specified index and updates focus.
//    * @param index The index of the token to remove.
//    * @param type The type of the token (for validation, unused).
//    */
//   removeToken: (index, type) => {
//     const {tokens, defaultInputRef, updateCounterFromTokens} = get();
//     const newTokens = tokens.filter((_, i) => i !== index);
//     set({tokens: newTokens});
//     if (newTokens.length === 0) {
//       defaultInputRef.current?.focus();
//     } else if (
//       index > 0 &&
//       (newTokens[index - 1]?.type === 'text-input' ||
//         newTokens[index - 1]?.type === 'image-file' ||
//         newTokens[index - 1]?.type === 'image-url' ||
//         newTokens[index - 1]?.type === 'audio-file' ||
//         newTokens[index - 1]?.type === 'audio-url')
//     ) {
//       newTokens[index - 1]?.ref?.current?.focus();
//     }
//     updateCounterFromTokens(newTokens);
//   },

//   /**
//    * Updates the value of a text-input token at the specified index.
//    * @param index The index of the token to update.
//    * @param value The new value for the token.
//    */
//   updateToken: (index, value) => {
//     const {tokens, updateCounterFromTokens} = get();
//     const newTokens = [...tokens];
//     if (newTokens[index]?.type === 'text-input') {
//       newTokens[index].value = value;
//     }
//     set({tokens: newTokens});
//     updateCounterFromTokens(newTokens);
//   },

//   /**
//    * Picks an image and adds it as a token, then updates counters and focus.
//    */
//   handleImagePick: async () => {
//     const {tokens, currentFocusedIndex, defaultInputRef, setTokens, updateCounterFromTokens} = get();
//     const res = await launchImageLibrary({
//       mediaType: 'photo',
//       includeBase64: false,
//     });

//     const asset = res?.assets?.[0];
//     if (!asset?.uri) return;

//     const newImageToken: Token = {
//       type: 'image-file',
//       value: asset.uri,
//       fileName: asset.fileName,
//       asset,
//     };

//     const newTokens: Tokens =
//       currentFocusedIndex === -1 ? [...tokens, newImageToken] : [...tokens.slice(0, currentFocusedIndex + 1), newImageToken, ...tokens.slice(currentFocusedIndex + 1)];

//     set({tokens: newTokens});
//     updateCounterFromTokens(newTokens);
//     defaultInputRef.current?.focus();
//   },

//   /**
//    * Moves focus to the next text-input token or default input.
//    * @param currentIndex The current token index.
//    */
//   goToNextToken: currentIndex => {
//     const {tokens, defaultInputRef, setCurrentFocusedIndex} = get();
//     if (tokens.length === 0) return;

//     for (let i = currentIndex + 1; i < tokens.length; i++) {
//       if (tokens[i].type === 'text-input') {
//         tokens[i].ref?.current?.focus();
//         setCurrentFocusedIndex(i);
//         return;
//       }
//     }
//     defaultInputRef.current?.focus();
//     setCurrentFocusedIndex(-1);
//   },

//   /**
//    * Updates the counter state based on the tokens array.
//    * @param tokensToCount The tokens to count.
//    */
//   updateCounterFromTokens: tokensToCount => {
//     let charCount = 0;
//     let imageCount = 0;
//     let audioCount = 0;

//     for (const token of tokensToCount) {
//       if (token.type === 'text-input') {
//         const text = String(token.value);
//         charCount += text.trim().length > 0 ? text.length : 1;
//       }
//       if (token.type === 'image-file' || token.type === 'image-url') {
//         imageCount += 1;
//         charCount += 1;
//       }
//       if (token.type === 'audio-file' || token.type === 'audio-url') {
//         audioCount += 1;
//         charCount += 1;
//       }
//     }

//     set({counter: {charCount, imageCount, audioCount}});
//   },
// }));
