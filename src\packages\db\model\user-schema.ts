import {Model, tableSchema} from '@nozbe/watermelondb';
import {date, field, text} from '@nozbe/watermelondb/decorators';

// Define the schema for user_info table
export const userInfo = tableSchema({
  name: 'user_info',
  columns: [
    {name: 'username', type: 'string', isOptional: true},
    {name: 'user_id', type: 'string'},
    {name: 'avatar', type: 'string'},
    {name: 'location', type: 'string'},
    {name: 'bio', type: 'string'},
    {name: 'email', type: 'string'},
    {name: 'playlist_link', type: 'string'},
    {name: 'gender', type: 'string'},
    {name: 'age', type: 'string'},
    {name: 'id_show_current_mood_on_profile', type: 'boolean'},
    {name: 'is_signup_completed', type: 'boolean'},
    {name: 'access_token', type: 'string'},
    {name: 'refresh_token', type: 'string'},
    {name: 'last_updated_at', type: 'number'},
    {name: 'is_authenticated', type: 'boolean'},
    {name: 'total_post_count', type: 'number'},
    {name: 'total_heal_count', type: 'number'},
    {name: 'total_followers', type: 'number'},
    {name: 'total_following', type: 'number'},
  ],
});

// Define the UserModel class
export class UserModel extends Model {
  static table = 'user_info';

  @text('username') username!: string;
  @text('avatar') avatar!: string;
  @text('location') location!: string;
  @text('bio') bio!: string;
  @text('email') email!: string;
  @text('playlist_link') playlistLink!: string;
  @text('gender') gender!: string;
  @text('age') age!: string;
  @field('id_show_current_mood_on_profile') idShowCurrentMoodOnProfile!: boolean;
  @field('is_signup_completed') isSignupCompleted!: boolean;
  @text('access_token') accessToken!: string;
  @text('refresh_token') refreshToken!: string;
  @date('last_updated_at') lastUpdatedAt!: number;
  @field('is_authenticated') isAuthenticated!: boolean;
  @field('total_post_count') totalPostCount!: number;
  @field('total_heal_count') totalHealCount!: number;
  @field('total_followers') totalFollowers!: number;
  @field('total_following') totalFollowing!: number;
}
