import AppLogo from '@assets/svgs/app-logo-white.svg';
import { View } from '@components/native';
import { useAuth } from '@context/index';
import { lazy, Suspense } from 'react';

const AuthScreens = lazy(() => import('./auth-screens'));
const UnAuthScreens = lazy(() => import('./unauth-screens'));

const AppNavigator = () => {
  const { isAuthenticated, isInitialLoading, isLoading, session } = useAuth();
  if (isInitialLoading) {
    return <SplashScreen />;
  }

  if (true) {
    return (
      <Suspense fallback={null}>
        <AuthScreens isSignupCompleted={true} />
      </Suspense>
    );
  }

  return (
    <Suspense fallback={null}>
      <UnAuthScreens />
    </Suspense>
  );
};

export default AppNavigator;

const SplashScreen = () => {
  return (
    <View flex={1} bg="purple500" flexCenterRow>
      <AppLogo fill="white" />
    </View>
  );
};
