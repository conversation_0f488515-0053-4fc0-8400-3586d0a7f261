import {ThemeColors} from '@/types/color-types';
// Put colors here if they arent present in theme colors , dont add colors which arent present without team discussion
// Use styles={{color:"color code"}} instead of color or bg props from @components/native
// #8E97FD

export const LightColors: ThemeColors = {
  // Base Colors
  foreground: '#000',
  background: '#fff',
  text: '#000',

  // Non Themed Colors (to be same hex code in dark mode as well)
  transparent: 'transparent',
  white: '#fff',
  black: '#000',

  // Extras
  lightBlue: '#F1F5FE',
  purpleLight: '#ECE2F2',
  orange: '#FFDCC4',

  // Purple
  purple100: '#EAE9F1',
  purple200: '#D2CFE2',
  purple300: '#BDB9D4',
  purple400: '#A5A0C5',
  purple500: '#8F89B7',
  purple600: '#69619E',
  purple700: '#504978',
  purple800: '#34304F',
  purple900: '#1B1929',

  // Neutral
  neutral00: '#F4F4F4',
  neutral10: '#E9E9E9',
  neutral20: '#D2D2D2',
  neutral30: '#BCBCBC',
  neutral40: '#A5A5A6',
  neutral50: '#8F8F8F',
  neutral60: '#797979',
  neutral70: '#626263',
  neutral80: '#4C4C4D',
  neutral90: '#353536',
  neutral100: '#1F1F20',

  // Negative
  negative00: '#FBF3F3',
  negative10: '#F7E7E6',
  negative20: '#E7B5B0',
  negative30: '#DC918A',
  negative40: '#CB5F54',
  negative50: '#C14033',
  negative60: '#B21000',
  negative70: '#A20F00',
  negative80: '#7E0B00',
  negative90: '#620900',
  negative100: '#4B0700',

  // Warning
  warning00: '#FDFBF3',
  warning10: '#FCF6E6',
  warning20: '#F6E2B0',
  warning30: '#F2D38A',
  warning40: '#ECBF54',
  warning50: '#E9B333',
  warning60: '#E3A000',
  warning70: '#CF9200',
  warning80: '#A17200',
  warning90: '#7D5800',
  warning100: '#5F4300',

  // Positive
  positive00: '#f3fbf7',
  positive10: '#E6F7EE',
  positive20: '#B0E5C9',
  positive30: '#8AD8AF',
  positive40: '#54C68A',
  positive50: '#33BB73',
  positive60: '#00AA50',
  positive70: '#009B49',
  positive80: '#007939',
  positive90: '#005E2C',
  positive100: '#004722',
};

/**
 * Need to reverse the themed color later when implmeneting dark mode.
 */

export const DarkColors: ThemeColors = {
  // Base colors
  foreground: '#fff',
  background: '#000',
  text: '#fff',

  // Non Themed Colors (same as LightColors)
  transparent: 'transparent',
  white: '#fff',
  black: '#000',

  // Extras (same as LightColors)
  lightBlue: '#F1F5FE',
  purpleLight: '#ECE2F2',
  orange: '#FFDCC4',

  // Purple (reversed from LightColors)
  purple100: '#1B1929', // LightColors purple900
  purple200: '#34304F', // LightColors purple800
  purple300: '#504978', // LightColors purple700
  purple400: '#69619E', // LightColors purple600
  purple500: '#8F89B7', // LightColors purple500
  purple600: '#A5A0C5', // LightColors purple400
  purple700: '#BDB9D4', // LightColors purple300
  purple800: '#D2CFE2', // LightColors purple200
  purple900: '#EAE9F1', // LightColors purple100

  // Neutral (reversed from LightColors)
  neutral00: '#1F1F20', // LightColors neutral100
  neutral10: '#353536', // LightColors neutral90
  neutral20: '#4C4C4D', // LightColors neutral80
  neutral30: '#626263', // LightColors neutral70
  neutral40: '#797979', // LightColors neutral60
  neutral50: '#8F8F8F', // LightColors neutral50
  neutral60: '#A5A5A6', // LightColors neutral40
  neutral70: '#BCBCBC', // LightColors neutral30
  neutral80: '#D2D2D2', // LightColors neutral20
  neutral90: '#E9E9E9', // LightColors neutral10
  neutral100: '#F4F4F4', // LightColors neutral00

  // Negative (reversed from LightColors)
  negative00: '#4B0700', // LightColors negative100
  negative10: '#620900', // LightColors negative90
  negative20: '#7E0B00', // LightColors negative80
  negative30: '#A20F00', // LightColors negative70
  negative40: '#B21000', // LightColors negative60
  negative50: '#C14033', // LightColors negative50
  negative60: '#CB5F54', // LightColors negative40
  negative70: '#DC918A', // LightColors negative30
  negative80: '#E7B5B0', // LightColors negative20
  negative90: '#F7E7E6', // LightColors negative10
  negative100: '#FBF3F3', // LightColors negative00

  // Warning (reversed from LightColors)
  warning00: '#5F4300', // LightColors warning100
  warning10: '#7D5800', // LightColors warning90
  warning20: '#A17200', // LightColors warning80
  warning30: '#CF9200', // LightColors warning70
  warning40: '#E3A000', // LightColors warning60
  warning50: '#E9B333', // LightColors warning50
  warning60: '#ECBF54', // LightColors warning40
  warning70: '#F2D38A', // LightColors warning30
  warning80: '#F6E2B0', // LightColors warning20
  warning90: '#FCF6E6', // LightColors warning10
  warning100: '#FDFBF3', // LightColors warning00

  // Positive (reversed from LightColors)
  positive00: '#004722', // LightColors positive100
  positive10: '#005E2C', // LightColors positive90
  positive20: '#007939', // LightColors positive80
  positive30: '#009B49', // LightColors positive70
  positive40: '#00AA50', // LightColors positive60
  positive50: '#33BB73', // LightColors positive50
  positive60: '#54C68A', // LightColors positive40
  positive70: '#8AD8AF', // LightColors positive30
  positive80: '#B0E5C9', // LightColors positive20
  positive90: '#E6F7EE', // LightColors positive10
  positive100: '#f3fbf7', // LightColors positive00
};
