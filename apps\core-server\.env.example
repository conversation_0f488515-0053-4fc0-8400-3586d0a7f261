# .{yourdomain}
COOKIE_DOMAIN = localhost 
ACCESS_TOKEN_SECRET = BNcxV/f7xZpN1AE8dZKgwbFQIN3pRIJWFQ73Z9yTUInYHrjBp/1dEGOlNrG3HsWPuOovRu/1dJirQvOl14SzVA==
REFRESH_TOKEN_SECRET = cO6qkpajh9YclPsyX06t9dKHIccWyWbsCRVdecPTGXXibRu8Dl9745KbFbcJck6dPcQ41VhHfSY+Si0M+7/PMw==

NODE_ENV = development
PORT = 3000

SERVER_URL = http://localhost:3000

# DATABASE_URL = libsql://express-template-aditya-kumarr.turso.io
# DATABASE_AUTH_TOKEN = ***************************************************************************************************************************************************************************************************************************

# DATABASE_URL = postgres://postgres:<EMAIL>:5555/test
DATABASE_URL =
# mail
SENDER_MAIL = 
# smtp
SMTP_HOST = 
SMTP_USER = 
SMTP_PASSWORD = 
SMTP_PORT =
# for admin panel
CLIENT_URL= http://localhost:8000
