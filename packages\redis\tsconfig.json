{"extends": "@repo/typescript-config/base.json", "compilerOptions": {"strict": true, "strictNullChecks": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "declarationMap": false, "declaration": false, "outDir": "./dist", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "typeRoots": ["./node_modules/@types", "./types"]}, "include": ["**/*.ts", "**/*.tsx", "src/**/*.ts", "src/**/*.d.ts", "drizzle.config.ts", "../lib/src/test.ts"], "exclude": ["drizzle.config.ts"]}