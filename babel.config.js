module.exports = {
  presets: ['module:@react-native/babel-preset'],
  plugins: [
    [
      'module-resolver',
      {
        root: ['./src'],
        extensions: ['.native.ts', '.native.tsx', '.ts', '.tsx', '.ios.ts', '.ios.tsx', '.android.ts', '.android.tx'],
        alias: {
          '@assets': './assets',
          '@assets/svgs': './assets/svgs',
          '@assets/images': './assets/images',
          '@assets/fonts': './assets/fonts',
          '@components': './src/components',
          '@config': './src/config',
          '@constants': './src/constants',
          '@context': './src/context',
          '@features': './src/features',
          '@utils': './src/utils',
          '@hooks': './src/hooks',
          '@navigation': './src/navigation',
          '@packages': './src/packages',
          '@types': './src/types',
          '@src': './src',
          // add other aliases as needed
        },
      },
    ],
    ['@babel/plugin-proposal-decorators', {legacy: true}],
    'react-native-reanimated/plugin',
  ],
};
