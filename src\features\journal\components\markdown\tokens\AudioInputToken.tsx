import DeleteIcon from '@assets/svgs/delete-icon.svg';
import {Text, View} from '@components/native';
import {BounceTap, Ripple} from '@components/shared/animated';
import {useTheme} from '@context/index';
import {TypeEnum} from '@features/journal/store/journal-store';
import React, {useRef, useState} from 'react';
import {Dimensions} from 'react-native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import FoundationIcon from 'react-native-vector-icons/Foundation';

export interface AudioInputTokenProps {
  value: string;
  index: number;
  removeMe?: (myIndex: number, type: TypeEnum) => void;
}

const {width} = Dimensions.get('screen');

const AudioInputToken: React.FC<AudioInputTokenProps> = ({index, value, removeMe}) => {
  const MAX_WIDTH = width * 0.8 - 30;
  const {colors} = useTheme();
  const audioRecorderPlayer = useRef(new AudioRecorderPlayer()).current;

  const [playing, setPlaying] = useState(false);
  const [time, setTime] = useState('0 : 00');
  const [progress, setProgress] = useState(0); // From 0 to 1
  const [lastPosition, setLastPosition] = useState(0);

  const startPlayback = async () => {
    setPlaying(true);
    await audioRecorderPlayer.startPlayer(value);
    await audioRecorderPlayer.seekToPlayer(lastPosition); // resume from last

    audioRecorderPlayer.addPlayBackListener(e => {
      const totalSec = Math.floor(e.currentPosition / 1000);
      const min = Math.floor(totalSec / 60);
      const sec = totalSec % 60;
      setTime(`${min} : ${sec < 10 ? '0' : ''}${sec}`);

      if (e.duration > 0) {
        setProgress(e.currentPosition / e.duration);
      }

      setLastPosition(e.currentPosition);

      if (e.currentPosition >= e.duration) {
        stopPlayback(true); // finished
      }
    });
  };

  const stopPlayback = async (finished = false) => {
    setPlaying(false);
    await audioRecorderPlayer.stopPlayer();
    audioRecorderPlayer.removePlayBackListener();

    if (finished) {
      setTime('0 : 00');
      setProgress(0);
      setLastPosition(0);
    }
  };

  const togglePlayback = () => {
    if (playing) {
      stopPlayback();
    } else {
      startPlayback();
    }
  };

  return (
    <View bg="purple100" pr={10} pl={5} py={10} btl={20} btr={20} bbr={20} w={MAX_WIDTH} disableSizeMatter maw={MAX_WIDTH}>
      <View display="flex" gap={10} fd="row" ai="center">
        <Ripple onPress={togglePlayback}>
          <View miw={40} flexCenterColumn>
            <View p={1}>{playing ? <FoundationIcon size={26} name="pause" /> : <FoundationIcon size={26} name="play" />}</View>
            <Text fs="10" color="neutral80" fw="500">
              {time}
            </Text>
          </View>
        </Ripple>

        <View flex={1} h={2} left={-4} mr={14} pos="relative">
          <View h={1.5} br={100} bg="purple500" />
          <View top={-3} pos="absolute" left={`${progress * 100}%`} size={8} br={100} bg="purple500" />
        </View>
      </View>

      <View pos="absolute" top="50%" right={-10} size={28} br={100} bw={1} bc="negative20" bg="background" flexCenterRow>
        <BounceTap onPress={() => removeMe?.(index, 'audio-file')}>
          <View size={25} flexCenterRow>
            <DeleteIcon fill={colors.negative50} />
          </View>
        </BounceTap>
      </View>
    </View>
  );
};

export default AudioInputToken;
