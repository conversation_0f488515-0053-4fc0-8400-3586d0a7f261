// Endpoints for stitch

// CHAT RELATED APIS
// /api/chat/recommended-stichers - non-paginated (best stitchers)
interface RecommendedStitchersResponse {
id: string;
totalHealedCount: number;
username: string;
avatar: string;
chatStatus: 'requested' | 'pending' | 'none'; // Is pending button text
}

// /api/chat/conversations?page=1&limit=50 - paginated (for fetching list of accepted or ongoing chat list)
interface ChatListItem {
id: string;
avatar: string;
conversationId: string;
username: string;
status: 'online' | 'offline';
lastMessage?: string;
unreadMessageCount?: number;
canInitiateChat: boolean; // Blocked or Not
pinnedBy: ['userid1', 'userid2']; // can be empty or can be included both , if i mutate for pinning store my user id and vice versa
}
// /api/chat/search?q=username - for searching the chat list , same interface as above
type ChatSearchItem = ChatListItem;

// /api/chat/history/:conversationId - paginated (for fecthing messages of that particular chat)
interface ChatMessageItem {
id: string;
message?: string;
sendBy: string; // userId
linkPreview?: {
websiteUrl: string;
previewUrl?: string;
preview?: string;
mimeType?: string;
};
// files?: [{url: string; mimetype: string; aspectRatio: string}]; // can be added for sharing photos , videos or audios later !!
}

// /api/chat/interaction-healed/:conversationid // for submitting the review from user a to b or b to a as the conversation helped them to heal or not
interface Nothing {}

// /api/chat/pin/:conversationId - mutation endpoint for pinning the chat from user a to user b or user b to user a , storing userid
interface Nothing {}

// /api/chat/block/:conversationId
interface Nothing {}

// /api/chat/report/:conversationId
interface Nothing {}

// Feed RELATED APIS below

// /api/feed?page=1&limit=10 (paginated)

interface FeedPostItem {
id: string;
avatar: string;
username: string;
photos: [
{
preview: string;
image: string;
},
]; // for now only single photo uploading feature is enabled
uploadedAt: Date; // eg 12 min Ago
totalLikesCount: number;
totalCommentCount: number;
title: string;
description: string; // a markdown can contain #tags
isBookmarked: boolean; //is saved or not by the user
}

// /api/feed/add/bookmark/:postId (mutation)
interface Nothing {}
// /api/feed/remove/bookmark/:postId (mutation)
interface Nothing {}

// /api/feed/post/create - for creating a new post
interface FormData {
title: string;
description: string; //markdown
image: string; // file or image data
allowLikeCommentPublic: boolean; // anyone can like or comment ?
//Poll related below
question: string;
option1: string;
option2: string;
option3: string; // opts are min 2 and
option4: string; // max 4 options
allowMultipleAnswers: boolean; // poll specific
}

// /api/feed/post/edit/:postId - for editing post and its poll
interface FormData {
// same as create form data
}

// /api/notifications?page=1&size=20
// sorted by newest to oldest
interface NotificationItem {
id: string;
avatar: string;
type: 'post' | 'chat-request';
url: string;
suffixImage: string; // image preview of that post
createdAt: string; // a time of that notification
}

// /api/feed/post/comments?page=1&size=10 &parentCommentId // this is when user scrolling nested comments

interface CommentItem {
id: string;
avatar: string;
username: string;
comment: string; // actual comment content
uploadedAt: string; // date time when commented
parentCommentId: boolean | null; // only present when scrolling in nested comment
totalRepliesCount: number; //only for parent comment usecase
}

// / Journal RELATED APIS below

// /api/journal/types?page=1&limit=100 // this will fetch the cards or types of journal object
interface JournalType {
id: string;
title: string; // Journal Type eg. Gratitude , etc
totalEntryCount: number;
image: string; // a BG image url for specific journal
lastUpdated: string; // a time stamp when user added any journal in specific type
}

// /api/journal/list?page=1&limit=50&type=all // (paginated) (by default all and can be other like Gratitude , etc)
interface JournalListItem {
id: string;
title: string;
description: string; // a markdown of the journal..
avatar: string; // a left side avatar for specific journal , its different from BG image , its the 1st image url which user uploaded
uploadedAt: string; // timestamp of creation
type: string; // Journal Category Type
}

// USER Profile Related Apis below
// /api/user/me -- my profile or session data
interface UserResponse {
fullName: string;
username: string;
avatar: string;
location: string;
currentMood: string;
totalPostCount: number;
totalHealCount: number;
gender: 'male' | 'female' | 'other';
bio: string; // (or description)
showCurrentMoodOnProfile: boolean;
playlistLink: string;
} // for auto login or sigin , below is the reponse !! should be same schema for login , signin , or autologin

interface SessionResponse {
accessToken: string;
refreshToken: string;
user: UserResponse | null; // (if singup completed)
isSignupCompleted: boolean;
email: string;
suggestedUsername?: string; // only present when signup isnt completed yet.
}

// /api/user/profile/update -- my profile edit or update
interface RequestBody {
// same all optional values can be sent as User Response Interface
}
