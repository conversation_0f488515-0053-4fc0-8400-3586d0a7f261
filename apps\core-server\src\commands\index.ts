// import { Constants } from "@/config/constants.js";
import "@repo/lib/env";
import type { Command } from "@/lib/command-module.js";
import { CommandExecuter } from "@/lib/command-module.js";
import generateTokenSecrets from "./list/generate-token-secrets.js";
import { listRoutes } from "./list/routes.js";
import { test } from "./list/test.js";
import populateAvatars from "./list/populate-avatars.js";
import { addQuestions } from "./list/population-questions.js";
import { populateLogs } from "./list/populate-logs.js";
import { removeLogs } from "./list/remove-logs.js";
import { populateJournalCategories } from "./list/populate-journalcategory.js";

// add your commands in this array
const commands: Command[] = [
  generateTokenSecrets,
  listRoutes,
  test,
  populateAvatars,
  addQuestions,
  populateLogs,
  removeLogs,
  populateJournalCategories,
];

//this will register all the commands
CommandExecuter.registerAll(commands);
CommandExecuter.start();
