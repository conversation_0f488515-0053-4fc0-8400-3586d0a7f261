import LocationPin from '@assets/svgs/gps-location.svg';
import {Pressable, Text, View} from '@components/native';
import {LoaderCentered} from '@components/shared/animated';
import {useBottomSheet} from '@components/shared/bottom-sheet/BottomSheet';
import {useTheme} from '@context/index';
import {BottomSheetFlashList} from '@gorhom/bottom-sheet';
import useStitch from '@packages/useStitch';
import {CountryItem} from '@packages/useStitch/types/public-api-types';
import React, {useState} from 'react';
import {ScrollView, TouchableOpacity} from 'react-native';

interface CountryList {
  closeSheet?: () => void;
  onSelect?: (country: CountryItem) => void;
}
export const CountryList: React.FC<CountryList> = ({closeSheet, onSelect}) => {
  // const {control, handleSubmit, setValue, watch} = useForm({
  //   defaultValues: {
  //     search: '',
  //   },
  // });
  const {colors} = useTheme();
  const {data, isLoading} = useStitch('getCountryList');
  // const searchQuery = watch('search'); // Watch the search input value
  // const [filteredCountries, setFilteredCountries] = useState<CountryItem[]>([]);

  // Effect to filter countries based on search query
  // useEffect(() => {
  //   if (data?.data) {
  //     if (!searchQuery) {
  //       // If search is empty, show all countries
  //       setFilteredCountries(data.data);
  //     } else {
  //       // Filter countries using regex case-insensitive search
  //       const regex = new RegExp(searchQuery, 'i');
  //       const filtered = data.data.filter(country => regex.test(country.name));
  //       setFilteredCountries(filtered);
  //     }
  //   }
  // }, [searchQuery, data]);

  return (
    <>
      {isLoading ? (
        <LoaderCentered />
      ) : (
        <ScrollView style={{flex: 1}}>
          <BottomSheetFlashList
            // ListHeaderComponent={() => (
            //   <View px={16} pt={10}>
            //     <TextInput
            //       labelType="background"
            //       onSuffixIconPress={() => setValue('search', '')}
            //       name="search"
            //       label="Search"
            //       control={control}
            //       prefixIcon={
            // <Pressable pt={2} onPress={closeSheet}>
            //   <Ionicons name="arrow-back-outline" color={colors.neutral70} size={22} />
            // </Pressable>
            //       }
            //     />
            //   </View>
            // )}
            scrollEnabled
            estimatedItemSize={200}
            data={data?.data}
            contentContainerStyle={{paddingBottom: 50}}
            renderItem={({item, index}) => {
              return (
                <TouchableOpacity
                  key={index}
                  onPress={() => {
                    onSelect?.(item);
                    closeSheet?.();
                  }}>
                  <View key={index} py={10} px={20}>
                    <Text>
                      {`${item.flag} `} {item.name}
                    </Text>
                  </View>
                </TouchableOpacity>
              );
            }}
          />
        </ScrollView>
      )}
    </>
  );
};

interface CountryListTextInput {
  onSelect?: (country: CountryItem) => void;
  initialValue?: string;
}
export const CountryListTextInput: React.FC<CountryListTextInput> = ({initialValue, onSelect}) => {
  const {Sheet, openSheet, closeSheet} = useBottomSheet({snapPoints: ['100%']});
  const [selectedCountry, setSelectedCountry] = useState<CountryItem | null>(null);
  return (
    <>
      <Pressable onPress={openSheet}>
        <View bg="neutral10" px={16} br={14} my={10} py={16}>
          <View display="flex" fd="row" jc="space-between" ai="center">
            <Text fs="12" fw="500" color={initialValue ? 'neutral80' : 'neutral70'}>
              {initialValue ?? 'Country'}
            </Text>
            <LocationPin />
          </View>
        </View>
      </Pressable>
      <Sheet title="Select Country">
        <CountryList onSelect={onSelect} closeSheet={closeSheet} />
      </Sheet>
    </>
  );
};
