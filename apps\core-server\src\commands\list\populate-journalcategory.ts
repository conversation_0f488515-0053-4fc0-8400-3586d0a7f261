import { Command } from "@/lib/command-module.js";
import { suggestUniqueUsername } from "@/routes/user/lib.js";
import db from "@repo/db";
import {
  journalCategoryTable,
  moodTable,
  notificationsTable,
} from "@repo/db/schema";

import { logger, pathGenerator, S3Bucket } from "@repo/lib";
import dayjs from "dayjs";
import { sql } from "drizzle-orm";
import path from "path";

const MOCK_JOURNAL_TYPES = [
  {
    type: "all",
    title: "All",
    totalEntries: 2,
    bgImage: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/all-journal-bg.png",
    ),
  },
  {
    type: "gratitude",
    title: "Gratitude",
    totalEntries: 20,
    bgImage: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/gratitude-journal-bg.png",
    ),
  },
  {
    type: "lost",
    title: "Lost",
    totalEntries: 20,
    bgImage: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/lost-journal-bg.png",
    ),
  },
  {
    type: "anger",
    title: "Anger",
    totalEntries: 18,
    bgImage: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/anger-journal-bg.png",
    ),
  },
  {
    type: "love",
    title: "Love",
    totalEntries: 10,
    bgImage: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/love-journal-bg.png",
    ),
  },
  {
    type: "anxiety",
    title: "Anxiety",
    totalEntries: 8,
    bgImage: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/anxiety-journal-bg.png",
    ),
  },
];

export const populateJournalCategories = new Command({
  name: "populate-journal-category",
  description: "Add demo journal categories",
  fn: async () => {
    logger.info("Task Started");
    await S3Bucket.uploadDirectory(
      path.join(process.cwd(), "src/storage/journal-categories"),
      "default-journal-categories",
    );

    // await db.insert(journalCategoryTable).values(MOCK_JOURNAL_TYPES);
    logger.info("Task Done");
  },
});
