import {
  integer,
  pgTable,
  timestamp,
  unique,
  uuid,
  varchar,
} from "drizzle-orm/pg-core";
import { users } from "./common.js";

export const referralTable = pgTable(
  "referrals",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    referralCode: varchar("referral_code", { length: 255 }).notNull(),
    issuer: uuid("issuer").references(() => users.id, {
      onDelete: "set null",
    }),
    usesAvailable: integer("uses_available").notNull().default(1),
    createdAt: timestamp("created_at").defaultNow(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [unique("referral_code_unique").on(table.referralCode)]
);

export const referralUsesTable = pgTable("referral_uses", {
  id: uuid("id").primaryKey().defaultRandom(),
  referralCode: varchar("referral_code").references(
    () => referralTable.referralCode,
    {
      onDelete: "cascade",
    }
  ),
  usedBy: uuid("usedBy").references(() => users.id, {
    onDelete: "set null",
  }),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date()),
});
