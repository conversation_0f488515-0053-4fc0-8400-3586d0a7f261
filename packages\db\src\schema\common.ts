import { aliasedTable, relations, sql } from "drizzle-orm";
import {
  pgTable,
  uuid,
  varchar,
  timestamp,
  boolean,
  date,
  pgEnum,
  text,
  numeric,
  integer,
  PgTransaction
} from "drizzle-orm/pg-core";

// Helper function to generate current timestamp

export const getAutoCancelDate = () => new Date(new Date().getTime() + 3 * 24 * 60 * 60 * 1000);

export const visibilityEnum = pgEnum("visibility_enum", ["public", "private", "unlisted"]);

export const profileVisibilityEnum = pgEnum("profile_visibility_enum", ["public", "private"]);

export const mediaTypeEnum = pgEnum("media_type_enum", [
  "image",
  "video",
  "audio",
  "document",
  "others"
]);

export const userRoleEnum = pgEnum("user_role", ["user", "admin"]);

export const users = pgTable("users", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: varchar("email", { length: 255 }).notNull().unique(),
  role: userRoleEnum("role").notNull().default("user"),
  refreshToken: varchar("refresh_token", { length: 255 }),
  verified: boolean("verified").default(false).notNull(),
  verifiedAt: timestamp("verified_at"),
  deletedAt: timestamp("deletedAt"),
  createdAt: timestamp("created_at").defaultNow(),
  lastLoginAt: timestamp("last_login_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

export const addressTable = pgTable("address", {
  id: uuid("id").primaryKey().defaultRandom(),
  line1: varchar("line1", { length: 255 }),
  line2: varchar("line2", { length: 255 }),
  city: varchar("city", { length: 255 }),
  state: varchar("state", { length: 255 }),
  country: varchar("country", { length: 255 }),
  countryCode: varchar("country_code", { length: 255 }),
  createdAt: timestamp("created_at").defaultNow(),
  postalCode: varchar("postal_code", { length: 255 }),
  lat: numeric("lat", { precision: 9, scale: 6 }),
  lng: numeric("lng", { precision: 9, scale: 6 }),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

export const filesTable = pgTable("files", {
  id: uuid("id").primaryKey().defaultRandom(),
  fileName: varchar("file_name", { length: 255 }).notNull(),
  url: text("url").notNull(),
  mimetype: text("mimetype").notNull(),
  preview: text("preview"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

export const avatarTable = pgTable("avatar", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("file_name", { length: 255 }).notNull(),
  url: text("url").notNull().unique(),
  visibility: visibilityEnum("visibility").notNull().default("public"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});
export const userAddressTable = aliasedTable(addressTable, "userAddressTable");

export const userDetails = pgTable("user_details", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  userName: varchar("user_name", { length: 255 }).notNull().unique(),
  avatarUrl: text("avatar_url"),
  gender: varchar("gender", { length: 255 }),
  bio: text("bio").notNull().default(""),
  dob: date("dob", { mode: "date" }),
  addressId: uuid("address_id").references(() => addressTable.id, {
    onDelete: "cascade"
  }),
  visibility: profileVisibilityEnum("visibility").notNull().default("public"),
  notifications: boolean("notifications").notNull().default(true),
  currentMood: varchar("current_mood", { length: 255 }),
  showCurrentMood: boolean("show_current_mood").notNull().default(true),
  playlistLink: text("playlist_link"),
  followersCount: integer("followers_count").notNull().default(0),
  followingCount: integer("following_count").notNull().default(0),
  totalPostCount: integer("total_post_count").notNull().default(0),
  totalHealCount: integer("total_heal_count").notNull().default(0),
  fcmToken: text("fcm_tokens"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

export const otps = pgTable("otp", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: varchar("email", { length: 255 }).notNull().unique(),
  phone: varchar("phone", { length: 50 }),
  status: varchar("status", { enum: ["pending", "verified"] })
    .notNull()
    .default("pending"),
  otp: varchar("otp", { length: 255 }).notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

// ----------------------------------------------------------------------------

// Request Tables

export const friendsTable = pgTable("friends", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  friendId: uuid("friend_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  status: varchar("status", {
    enum: ["pending", "accepted", "rejected", "uninterested", "blocked"]
  })
    .default("pending")
    .notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

export const blockListTable = pgTable("block_list", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  blockedUserId: uuid("blocked_user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

export const reportsTable = pgTable("reports", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  listingId: uuid("request_id"),
  reportedUserId: uuid("reported_user_id").references(() => users.id, {
    onDelete: "cascade"
  }),
  type: varchar("type", { length: 255 }),
  description: text("description").notNull(),
  resolved: boolean("resolved").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

export const faqTable = pgTable("faqs", {
  id: uuid("id").primaryKey().defaultRandom(),
  question: text("question").notNull(),
  answer: text("answer").notNull(),
  order: integer("order").notNull().unique(),
  visibility: varchar("visibility", { enum: ["public", "private"] })
    .notNull()
    .default("public"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});
