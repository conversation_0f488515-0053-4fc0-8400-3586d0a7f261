import { Router } from "express";
import { Auth<PERSON><PERSON>roller } from "./controller.js";
import { createSSOAuthRouter } from "./sso/router.js";

export function createAuthRouter() {
  const router = Router();
  router.post("/signup", AuthController.signup);
  router.post("/login", AuthController.login);
  router.post("/verify", AuthController.verify);
  router.post("/token", AuthController.refetchAccessToken);
  router.post("/logout", AuthController.logout);
  router.use("/sso", createSSOAuthRouter());
  return router;
}
// refresh
/**
 * 1. login
 * 2. issue an refresh token
 * 3. saved in user record as well
 * 4. send back refresh token
 * 5. you can use refresh token to get new access token
 * 6.
 */
