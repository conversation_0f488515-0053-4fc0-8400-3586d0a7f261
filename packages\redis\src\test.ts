import "@repo/lib/env";
import { redisClient } from "./index.js";

// const data = {
//   room1: [
//     {
//       messageId: 1,
//       senderId: 1,
//       message: "Hello",
//       timestamp: new Date(),
//     },
//     {
//       messageId: 2,
//       senderId: 1,
//       message: "hi",
//       timestamp: new Date(),
//     },
//     {
//       messageId: 3,
//       senderId: 1,
//       message: "Why",
//       timestamp: new Date(),
//     },
//   ],
// };

// data.room1.map((d) => {
//   redisClient.set(
//     `chat:room1:${d.messageId}`,
//     JSON.stringify(data.room1.find((d) => d.messageId === 1))
//   );
// });
const keys = await redisClient.keys("chat:room1:*");
await redisClient.mget(keys).then((...data) => {
  console.log(data);
});

// redisClient.set("key", "value");

// redisClient.set("user:1", JSON.stringify({ id: 1, name: "<PERSON>" }));

// redisClient.set("chat:100", JSON.stringify({ id: 100, messages: [] }));

// redisClient.set("chat:300:messageId", "1");
// redisClient.set("chat:300:message", "Hello");
// redisClient.set("chat:300:created_at", "");

// redisClient.set(
//   "chat:200",
//   JSON.stringify({
//     id: 200,
//     messages: [
//       {
//         senderId: 1,
//         message: "Hello",
//         createdAt: new Date(),
//       },
//       {
//         senderId: 2,
//         message: "Hi",
//         createdAt: new Date(),
//       },
//     ],
//   })
// );
// redisClient.flushdb();
