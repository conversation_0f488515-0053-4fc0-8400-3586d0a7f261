#!/bin/zsh

CORE_SERVER="core-server"
CHAT_SERVER="chat-server"
ENV="staging"

REPOS=(
  "$CORE_SERVER-$ENV"
  "$CHAT_SERVER-$ENV"
)

for repo in $REPOS; do
  echo "🔍 Checking repository: $repo"
  if aws ecr describe-repositories --repository-names "$repo" &>/dev/null; then
    echo "✅ Repository already exists: $repo"
  else
    echo "➕ Creating repository: $repo"
    aws ecr create-repository --repository-name "$repo"
  fi
done
