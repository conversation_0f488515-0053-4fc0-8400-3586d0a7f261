import { authValidator, bodyValidator } from "@/lib/validation-module.js";
import db from "@repo/db";
import { notificationsTable, userDetails } from "@repo/db/schema";
import { and, count, desc, eq, inArray } from "drizzle-orm";
import type { Request, Response, NextFunction } from "express";
import { markAsReadValidator, uploadFcmTokenValidator } from "./validator.js";
import { Infer } from "@vinejs/vine/types";
import { ErrorResponse } from "@/lib/error-handlers-module/index.js";
export class NotificationController {
  @authValidator()
  static async getNotifications(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const page = parseInt(req.query.page as string, 10) || 1;
      const limit = parseInt(req.query.limit as string, 10) || 10;
      const offset = (page - 1) * limit;
      const totalNotifications = await db
        .select({ count: count() })
        .from(notificationsTable)
        .where(eq(notificationsTable.userId, req.user.id))
        .then((result) => result[0]?.count || 0);
      const notifications = await db
        .select()
        .from(notificationsTable)
        .where(eq(notificationsTable.userId, req.user.id))
        .limit(limit)
        .offset(offset)
        .orderBy(desc(notificationsTable.createdAt));

      const totalPages = Math.ceil(totalNotifications / limit);

      res.json({
        data: {
          data: notifications,
          pagination: {
            total: totalNotifications,
            totalPages,
            currentPage: page,
            pageSize: limit,
          },
        },
      });
      return;
    } catch (error) {
      next(error);
    }
  }

  @authValidator()
  @bodyValidator(uploadFcmTokenValidator)
  static async uploadFcmToken(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof uploadFcmTokenValidator>;
      const [user] = await db
        .update(userDetails)
        .set({ fcmToken: payload.fcmToken })
        .where(eq(userDetails.userId, req.user.id))
        .returning();
      if (!user) throw new ErrorResponse("User not found", 404);
      res.json({ message: "FCM token uploaded", success: true });
      return;
    } catch (error) {
      next(error);
    }
  }
  @authValidator()
  @bodyValidator(markAsReadValidator)
  static async markAsRead(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof markAsReadValidator>;
      await db
        .update(notificationsTable)
        .set({ read: true })
        .where(
          and(
            eq(notificationsTable.userId, req.user.id),
            inArray(notificationsTable.id, payload.notificationIds),
          ),
        );
      res.json({ message: "Notifications marked as read", success: true });
      return;
    } catch (error) {
      next(error);
    }
  }
}
