// packages/useStitch/apis/profile-apis.ts
import {SessionUser} from '../types';
import {ApiResponse, MutationApiConfig} from '../types/common-api-types';
import {CompleteSignupMutationBody, UsernameAvailibilityResponse} from '../types/user-api-types';

export namespace UserApis {
  export const usernameAvailibility = 'username-availability' as const;
  export const completeSignup = 'complete-signup' as const;
  export const me = 'get-my-details';
}

export const userApiConfig = {
  usernameAvailibility: {
    path: '/user/username-availability',
    method: 'GET',
    protected: true,
    responseType: undefined as unknown as ApiResponse<UsernameAvailibilityResponse>,
    baseCacheKey: 'username-availibility',
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<UsernameAvailibilityResponse, any>,
  completeSignup: {
    path: '/user/complete-signup',
    method: 'POST',
    protected: true,
    mutationBody: undefined as unknown as CompleteSignupMutationBody,
    responseType: undefined as unknown as ApiResponse<UsernameAvailibilityResponse>,
    baseCacheKey: 'username-availibility',
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<CompleteSignupMutationBody, any>,
  me: {
    path: '/user/me',
    method: 'GET',
    protected: true,
    responseType: undefined as unknown as ApiResponse<SessionUser>,
    baseCacheKey: 'my-user-info',
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<SessionUser, any>,
};
