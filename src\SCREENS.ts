// /src/SCREENS.ts

const PUBLIC_SCREENS = {
  FAQS: 'Faqs',
  PRIVACY_POLICY: 'PrivacyPolicy',
  TERMS_OF_SERVICE: 'TermsOfService',
} as const;

const NON_AUTHENTICATED_SCREENS = {
  INTRO: 'Intro',
  PRE_AUTH: 'PreAuth',
  SIGNUP: 'Signup',
  LOGIN: 'Login',
  VERIFY_OTP: 'VerifyOtp',
} as const;

const CHAT_SCREENS = {
  CONVERSATIONS: 'Conversations', // Tab Screen 0th index
  CHAT: 'Chat',
} as const;

const FEED_SCREENS = {
  FEED: 'Feed', // Tab Screen 1th index
  CREATE_NEW_POST: 'CreateNewPost',
  CREATE_NEW_POLL: 'CreateNewPoll',
  EDIT_POST: 'EditPost',
  EDIT_POLL: 'EditPoll',
} as const;

const JOURNAL_SCREENS = {
  JOURNAL: 'Journal', // Tab Screen 2th index
  JOURNAL_SEARCH: 'JournalSearch',
  JOURNAL_DETAILS: 'JournalDetails',
  CHOOSE_JOURNAL_TYPE: 'ChooseJournalType',
  CREATE_JOURNAL_TYPE: 'CreateJournalType',
  ADD_JOURNAL: 'AddJournal',
  EDIT_JOURNAL: 'EditJournal',
} as const;

const MOOD_TRACKER_SCREENS = {
  MOOD_TRACKER: 'MoodTracker', // Tab Screen 3th index
  MOOD_CALENDAR: 'MoodCalendar',
  MOOD_ARTICLE: 'MoodArticle',
} as const;

const PROFILE_SCREENS = {
  GET_STARTED: 'GetStarted',
  ONBOARDING: 'Onboarding',
  PROFILE: 'Profile', // Tab Screen 4th index
  EDIT_PROFILE: 'EditProfile',
  SELECT_AVATAR: 'SelectAvatar',
  USERS_PROFILE: 'UsersProfile',
} as const;

const DRAWER_SCREENS = {
  BOOKMARKS: 'Bookmarks',
  BLOCKED_CONTACTS: 'BlockedContacts',
  SETTINGS: 'Settings',
} as const;

const SCREENS = {
  ...PUBLIC_SCREENS,
  ...NON_AUTHENTICATED_SCREENS,
  ...CHAT_SCREENS,
  ...FEED_SCREENS,
  ...JOURNAL_SCREENS,
  ...MOOD_TRACKER_SCREENS,
  ...PROFILE_SCREENS,
  ...DRAWER_SCREENS,
} as const;

export default SCREENS;
