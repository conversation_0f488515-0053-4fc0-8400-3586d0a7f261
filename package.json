{"name": "bmg-monorepo", "private": true, "type": "module", "scripts": {"build": "turbo build", "build:pac": "turbo build --filter=@repo/*", "build:admin": "turbo build --filter=admin-panel --filter=core-server", "dev": "turbo dev", "backend": "turbo dev --filter=core-server", "admin": "turbo dev --filter=core-server --filter=admin-panel", "web": "turbo dev --filter=core-server --filter=web-client", "serve": "turbo dev --filter=core-server --filter=cron-server --filter=chat-server", "command": "pnpm  --filter=core-server run cmd", "email": "turbo dev  --filter=@repo/email", "start": "turbo start", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "db:generate": "drizzle-kit generate --name", "db:push": "turbo build --filter=@repo/db && drizzle-kit push", "db:studio": "drizzle-kit studio", "setup-husky": "husky install"}, "devDependencies": {"@types/chai": "^5.0.1", "@types/mocha": "^10.0.9", "@types/node": "^22.9.0", "chai": "^5.1.2", "drizzle-kit": "^0.27.1", "esbuild": "^0.25.2", "mocha": "^10.8.2", "prettier": "^3.2.5", "supertest": "^7.0.0", "tsx": "^4.19.2", "turbo": "^2.5.3", "typescript": "5.5.4", "husky": "^8.0.0"}, "packageManager": "pnpm@8.15.6", "engines": {"node": ">=18"}, "dependencies": {"dotenv": "^16.4.5"}}