import AnxiousDay from '@assets/svgs/anxious-day.svg';
import FrustratedDay from '@assets/svgs/frustrated-day.svg';
import HealingDay from '@assets/svgs/healing-day.svg';
import HeartbrokenDay from '@assets/svgs/heartbroken-day.svg';
import OptimisticDay from '@assets/svgs/optimistic-day.svg';
import { Text, View } from '@components/native';
import { FontFamilyMap } from '@components/native/Text';
import { ScreenWrapper } from '@components/shared';
import { AnimatedFlashList } from '@components/shared/animated';
import { useTheme } from '@context/index';
import useStitch from '@packages/useStitch';
import React from 'react';
import { Calendar } from 'react-native-calendars';

const getSvgByMood = (mood: string) => {
  switch (mood) {
    case 'Heartbroken':
      return HeartbrokenDay;
    case 'Frustrated':
      return FrustratedDay;
    case 'Anxious':
      return AnxiousDay;
    case 'Optimistic':
      return OptimisticDay;
    case 'Healing':
      return HealingDay;
    default:
      return null;
  }
};

const today = new Date().toISOString().split('T')[0];

const DayComponent = ({ date, marking }: any) => {
  const mood = marking?.customStyles?.mood || marking?.mood || '';
  const MoodSvg = getSvgByMood(mood);
  const isToday = date.dateString === today;

  return (
    <View ai="center" jc="center">
      <View pos="absolute" w={30} h={27} br={21} ai="center" jc="center" bg={isToday ? 'purpleLight' : 'transparent'}>
        {MoodSvg && <MoodSvg width={36} height={36} />}
      </View>
      <Text fw={isToday ? '600' : '400'} fs="16" color="neutral80">
        {date.day}
      </Text>
    </View>
  );
};

export const MoodCalendar = () => {
  // Fetch mood calendar data from API
  const { mutateAsync, data: resp } = useStitch('getMoodCalender', {
    mutationOptions: {
      onSuccess: data => {
        console.log('Mood calendar fetched:', data);
      },
      onError: error => {
        console.error('Mood calendar error:', error);
      },
    },
  });

  React.useEffect(() => {
    mutateAsync();
  }, [mutateAsync]);

  const moodCalendarData = resp?.data || ([] as { value: string; date: string }[]);
  const { colors } = useTheme();

  // Helper function to format date as YYYY-MM-DD
  const formatDateString = (date: string) => {
    const parts = date.split('-');
    return `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
  };

  // Calculate marked dates from API data
  const markedDates = React.useMemo(() => {
    if (!Array.isArray(moodCalendarData)) return [];

    const monthlyData: { [key: string]: { [key: string]: any } } = {};

    moodCalendarData.forEach((entry: { value: string; date: string }) => {
      const dateString = formatDateString(entry.date);
      const [year, month] = dateString.split('-');
      const monthKey = `${year}-${month}`;

      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {};
      }

      monthlyData[monthKey][dateString] = {
        customStyles: {
          mood: entry.value,
        },
      };
    });

    return Object.values(monthlyData);
  }, [moodCalendarData]);

  return (
    <ScreenWrapper
      title={
        <Text ff="PlayfairDisplay-SemiBold" fs="20" fw="600">
          Mood Summary
        </Text>
      }>
      <View h={15} />
      <View fd="row" jc="space-between" ai="center" px={34}>
        <Text color="neutral80" fw="400" fs="10">
          S
        </Text>
        <Text color="neutral80" fw="400" fs="10">
          M
        </Text>
        <Text color="neutral80" fw="400" fs="10">
          T
        </Text>
        <Text color="neutral80" fw="400" fs="10">
          W
        </Text>
        <Text color="neutral80" fw="400" fs="10">
          T
        </Text>
        <Text color="neutral80" fw="400" fs="10">
          F
        </Text>
        <Text color="neutral80" fw="400" fs="10">
          S
        </Text>
      </View>
      <View h={1} bg="neutral20" mt={10} />
      <View flex={1}>
        <AnimatedFlashList
          data={markedDates}
          renderItem={({ item }: any) => (
            <Calendar
              markingType={'custom'}
              markedDates={item}
              dayComponent={DayComponent}
              disabledByDefault={true}
              disableAllTouchEventsForDisabledDays={true}
              disableAllTouchEventsForInactiveDays={true}
              onDayPress={undefined}
              onDayLongPress={undefined}
              initialDate={Object.keys(item)[0]}
              theme={{
                backgroundColor: colors.background,
                calendarBackground: colors.background,
                textDisabledColor: colors.neutral80,
                monthTextColor: colors.neutral80,
                textMonthFontFamily: FontFamilyMap['700'],
                textMonthFontSize: 16,
              }}
              hideArrows={true}
              disableMonthChange={true}
              hideExtraDays={true}
              hideDayNames={true}
            />
          )}
          estimatedItemSize={5}
        />
      </View>
    </ScreenWrapper>
  );
};
