# Getting Started with <PERSON><PERSON>

> **Note**: Make sure you have completed the [Set Up Your Environment](https://reactnative.dev/docs/set-up-your-environment) guide before proceeding.

## Step 1: Package Installation

First you need to install node modules , don't use pnpm or yarn , use npm instead.

```sh
npm i
```

## Step 2: Environment setup

Create a .env.dev file in root of your project and paste the env variables in that file.

## Step 3: Start Dev Server

Then open a terminal and do

# For IOS

```sh

npm run ios-dev

```

# For Android

```sh

npm run android-dev

```

# Learn More

To learn more about React Native, take a look at the following resources:

- [React Native Website](https://reactnative.dev) - learn more about React Native.
- [Getting Started](https://reactnative.dev/docs/environment-setup) - an **overview** of React Native and how setup your environment.
- [Learn the Basics](https://reactnative.dev/docs/getting-started) - a **guided tour** of the React Native **basics**.
- [Blog](https://reactnative.dev/blog) - read the latest official React Native **Blog** posts.
- [`@facebook/react-native`](https://github.com/facebook/react-native) - the Open Source; GitHub **repository** for React Native.
