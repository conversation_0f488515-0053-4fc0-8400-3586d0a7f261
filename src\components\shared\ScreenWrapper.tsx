import {AnimatedPressable, ViewProps as NativeViewProps, View as NView, Text} from '@components/native';
import {useTheme} from '@context/index';
import {useBottomSheetModal} from '@gorhom/bottom-sheet';
import {router} from '@navigation/refs/navigation-ref';
import React, {forwardRef, isValidElement, useEffect} from 'react';
import {BackHandler, Keyboard, Platform, ViewProps as RNViewProps, TouchableWithoutFeedback, View, ViewStyle} from 'react-native';
import {useAnimatedStyle, withTiming} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {moderateScale} from 'react-native-size-matters';
import {StackBackButton} from './StackBackButton';

interface ViewProps extends RNViewProps {
  px?: number;
  py?: number;
  hideHeader?: boolean;
  title?: string | React.ReactElement;
  hideBackButton?: boolean;
  customHeaderComponent?: React.ReactElement;
  leftHeaderProps?: NativeViewProps;
  headerStyles?: ViewStyle;
  withTouchableFeedback?: boolean;
}

/**
 * ScreenWrapper is a component that provides a consistent background color
 * and layout for screens in the application. It includes an animated overlay
 * that appears behind bottom sheets when any sheet is open. On Android, the
 * native back button closes all open bottom sheets before navigating back.
 *
 * @param {ViewProps} props - The properties passed to the View component.
 * @param {React.Ref<View>} ref - A ref to the View component.
 * @returns {JSX.Element} A styled View component with an optional overlay.
 */
export const ScreenWrapper = forwardRef<View, ViewProps>((props, ref) => {
  const {title, customHeaderComponent, headerStyles, hideBackButton = false, leftHeaderProps, withTouchableFeedback = true} = props;
  const {colors, sheetValue} = useTheme();
  const {top} = useSafeAreaInsets();
  const {dismissAll} = useBottomSheetModal();

  // Animated style for the overlay
  const overlayStyle = useAnimatedStyle(() => ({
    opacity: withTiming(sheetValue.get() ? 0.2 : 0, {duration: 300}),
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'black',
    zIndex: 5, // Below bottom sheet (zIndex > 10)
    pointerEvents: sheetValue.get() ? 'auto' : 'none',
  }));

  const defaultStyles: ViewStyle = {
    flex: 1,
    backgroundColor: colors.background,
    ...(props.px != undefined && {
      paddingHorizontal: moderateScale(props.px, 0.5),
    }),
    ...(props.py != undefined && {
      paddingVertical: moderateScale(props.py, 0.5),
    }),
  };

  const shadowWrapperStyles: ViewStyle = {
    paddingTop: top,
    backgroundColor: colors.background,
    ...Platform.select({
      ios: {
        shadowColor: '#000000',
        shadowOffset: {width: 0, height: 4},
        shadowOpacity: 0.1,
        shadowRadius: 6,
        zIndex: 10,
      },
      android: {
        elevation: 6,
        zIndex: 10,
      },
    }),
  };

  // Handle Android back button
  useEffect(() => {
    if (Platform.OS !== 'android') return;

    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (sheetValue.get()) {
        dismissAll();
        return true; // Prevent default back navigation
      }
      return false; // Allow default navigation
    });

    return () => backHandler.remove();
  }, [sheetValue, dismissAll]);

  let BaseChildren = (
    <View ref={ref} style={[defaultStyles, props.style]} {...props}>
      {props.children}
    </View>
  );

  if (withTouchableFeedback) {
    BaseChildren = <TouchableWithoutFeedback onPress={Keyboard.dismiss}>{BaseChildren}</TouchableWithoutFeedback>;
  }

  return (
    <NView style={{flex: 1, backgroundColor: colors.background}}>
      {/* Animated overlay for bottom sheet */}
      <AnimatedPressable onPress={dismissAll} style={overlayStyle} />

      {!props.hideHeader && (
        <View style={[shadowWrapperStyles, headerStyles]}>
          <NView px={20} display="flex" ai="center" fd="row" gap={10} py={10} style={{backgroundColor: colors.background}} {...leftHeaderProps}>
            {router.canGoBack() && !hideBackButton && <StackBackButton withAbsolute={false} />}
            {isValidElement(title)
              ? title
              : title != '' &&
                title != undefined && (
                  <Text ff="PlayfairDisplay-Medium" color="neutral80" fs="20" fw="700" numberOfLines={1}>
                    {title}
                  </Text>
                )}
          </NView>
          <AnimatedPressable onPress={dismissAll} style={overlayStyle} />
          {customHeaderComponent && <>{customHeaderComponent}</>}
        </View>
      )}
      {BaseChildren}
    </NView>
  );
});
