import {Text} from '@components/native';
import {useTheme} from '@context/index';
import {MotiView} from 'moti';
import React, {FC, memo, useCallback, useMemo, useState} from 'react';
import {Dimensions, Modal, Pressable, StyleSheet, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Svg, {Polygon} from 'react-native-svg';

// Screen dimensions
const {width} = Dimensions.get('window');
const DIALOG_SIZE = width * 0.8; // Square dialog size (80% of screen width)
const TRIANGLE_SIZE = 20; // Size of the bottom triangle

// Props for the Dialog component
interface DialogProps {
  title?: string;
  wrapWithView?: boolean;
  children: React.ReactNode;
}

/**
 * Hook to manage a centered, square-shaped dialog with a bottom triangle and animation.
 * @param options Optional configuration for the dialog.
 * @returns An object with openDialog, closeDialog, and Dialog component.
 */
export function useDialog(options?: {onDismiss?: () => void}) {
  const [isVisible, setIsVisible] = useState(false);
  const [content, setContent] = useState<React.ReactNode | null>(null);
  const {colors, theme} = useTheme();
  const {top} = useSafeAreaInsets();

  // Memoize onDismiss callback
  const onDismiss = useMemo(() => options?.onDismiss, [options?.onDismiss]);

  // Memoize handleDialogChanges to track visibility
  const handleDialogChanges = useCallback(() => {
    if (!isVisible) {
      onDismiss?.();
    }
  }, [isVisible, onDismiss]);

  // Open dialog with custom content
  const openDialog = useCallback((dialogContent: React.ReactNode) => {
    setContent(dialogContent);
    setIsVisible(true);
  }, []);

  // Close dialog
  const closeDialog = useCallback(() => {
    setIsVisible(false);
    setContent(null);
  }, []);

  // Memoized Dialog component
  const Dialog: FC<DialogProps> = memo(({title, wrapWithView = false, children}) => {
    const dialogContent = wrapWithView ? <View style={styles.content}>{children}</View> : children;

    return (
      <Modal visible={isVisible} transparent animationType="none" onRequestClose={closeDialog} onDismiss={handleDialogChanges}>
        <Pressable style={[styles.overlay, {backgroundColor: 'rgba(0,0,0,0.2)'}]} onPress={closeDialog}>
          <MotiView
            from={{translateY: -50}}
            animate={{translateY: 0}}
            transition={{type: 'timing', duration: 300}}
            style={[styles.dialog, {backgroundColor: theme === 'dark' ? colors.neutral80 : colors.neutral10}]}>
            {/* Title */}
            {title && <Text style={[styles.title, {color: colors.text}]}>{title}</Text>}
            {/* Content */}
            {dialogContent}
            {/* Bottom triangle */}
            <Svg width={TRIANGLE_SIZE} height={TRIANGLE_SIZE} style={styles.triangle}>
              <Polygon points={`0,0 ${TRIANGLE_SIZE},0 ${TRIANGLE_SIZE / 2},${TRIANGLE_SIZE}`} fill={theme === 'dark' ? colors.neutral80 : colors.neutral10} />
            </Svg>
          </MotiView>
        </Pressable>
      </Modal>
    );
  });

  Dialog.displayName = 'Dialog';

  // Memoize the hook's return value
  return useMemo(
    () => ({
      openDialog,
      closeDialog,
      Dialog,
    }),
    [openDialog, closeDialog],
  );
}

// Styles
const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialog: {
    width: DIALOG_SIZE,
    height: DIALOG_SIZE,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  content: {
    flex: 1,
    width: '100%',
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 10,
    textAlign: 'center',
  },
  triangle: {
    position: 'absolute',
    bottom: -TRIANGLE_SIZE / 2,
  },
});
