const path = require('path');
const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const {wrapWithReanimatedMetroConfig} = require('react-native-reanimated/metro-config');
const {wrapWithAudioAPIMetroConfig} = require('react-native-audio-api/metro-config');

const defaultConfig = getDefaultConfig(__dirname);
const {assetExts, sourceExts} = defaultConfig.resolver;

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
    babelTransformerPath: require.resolve('react-native-svg-transformer/react-native'),
  },
  resolver: {
    assetExts: assetExts.filter(ext => ext !== 'svg'),
    sourceExts: [...sourceExts, 'svg', 'cjs'],
    extraNodeModules: {
      '@assets': path.resolve(__dirname, 'assets'),
      '@assets/svgs': path.resolve(__dirname, 'assets/svgs'),
      '@components': path.resolve(__dirname, 'src/components'),
      '@config': path.resolve(__dirname, 'src/config'),
      '@constants': path.resolve(__dirname, 'src/constants'),
      '@context': path.resolve(__dirname, 'src/context'),
      '@features': path.resolve(__dirname, 'src/features'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@hooks': path.resolve(__dirname, 'src/hooks'),
      '@navigation': path.resolve(__dirname, 'src/navigation'),
      '@packages': path.resolve(__dirname, 'src/packages'),
      '@types': path.resolve(__dirname, 'src/types'),
      '@src': path.resolve(__dirname, 'src'),
    },
  },
  projectRoot: path.resolve(__dirname),
  // watchFolders: [path.resolve(__dirname, 'packages/stitch-markdown')],
};

module.exports = mergeConfig(defaultConfig, wrapWithAudioAPIMetroConfig(wrapWithReanimatedMetroConfig(config)));
