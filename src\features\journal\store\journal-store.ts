import {createRef} from 'react';
import {NativeSyntheticEvent, ScrollView, TextInput, TextInputKeyPressEventData} from 'react-native';
import {Asset, launchImageLibrary} from 'react-native-image-picker';
import {create} from 'zustand';

export type TypeEnum = 'text-input' | 'image-file' | 'image-url' | 'audio-file' | 'audio-url';
export type ValueEnum = string | number;

export type Token = {value: ValueEnum; type: TypeEnum; ref?: any; tokens?: Token[]; fileName?: string; asset?: Asset};
export type Tokens = Token[];

interface JournalStore {
  currentFocusedIndex: number;
  setCurrentFocusedIndex: (index: number) => void;
  inputValue: string;
  setInputValue: (value: string) => void;
  title: string;
  setTitle: (title: string) => void;
  tokens: Tokens; // Journal Description Inputs Array
  setTokens: (tokens: Tokens) => void;
  getUpdatedInputsAsync: () => Promise<Token[]>;
  onKeyPress: (e: NativeSyntheticEvent<TextInputKeyPressEventData>) => Promise<void>;
  removeToken: (index: number, type: TypeEnum) => void;
  updateToken: (index: number, value: string) => void;
  handleImagePick: () => Promise<void>;
  goToNextToken: (currentIndex: number) => void;
  defaultInputRef: React.RefObject<TextInput | null>;
  scrollViewRef: React.RefObject<ScrollView | null>;
  counter: {
    charCount: number;
    imageCount: number;
    audioCount: number;
  };
  chosenJournalTypeId: string;
  setChosenJournalTypeId: (setChosenJournalTypeId: string) => void;
  updateCounterFromTokens: (tokensToCount: Tokens) => void;
  handleAddAudio: (payload: {filePath: string}) => void;
  performCleanups: () => void;
}

const EVENT_KEYS = {
  enter: 'Enter',
  backspace: 'Backspace',
};

export const useJournalStore = create<JournalStore>((set, get) => ({
  currentFocusedIndex: -1,
  setCurrentFocusedIndex: index => set({currentFocusedIndex: index}),
  inputValue: '',
  title: '',
  setTitle: (title: string) => set({title}),
  setInputValue: value => set({inputValue: value}),
  tokens: [],
  setTokens: tokens => {
    set({tokens});
  },
  defaultInputRef: createRef<TextInput>(),
  scrollViewRef: createRef<ScrollView>(),
  counter: {
    charCount: 0,
    imageCount: 0,
    audioCount: 0,
  },
  getUpdatedInputsAsync: async () => {
    const {tokens, inputValue, setInputValue, setTokens, updateCounterFromTokens} = get();
    return new Promise(res => {
      const newToken: Token = {
        type: 'text-input' as TypeEnum, // Explicitly cast to TypeEnum
        value: inputValue,
        ref: createRef<TextInput>(),
      };
      const updatedTokens: Tokens = [...tokens, newToken];
      setTokens(updatedTokens);
      setInputValue('');
      updateCounterFromTokens(updatedTokens);
      res(updatedTokens);
    });
  },
  onKeyPress: async (e: NativeSyntheticEvent<TextInputKeyPressEventData>) => {
    const {currentFocusedIndex, setInputValue, inputValue, tokens, getUpdatedInputsAsync, updateCounterFromTokens, setTokens} = get();
    if (e.nativeEvent.key === EVENT_KEYS.backspace) {
      if (currentFocusedIndex === -1 && inputValue === '') {
        if (tokens.length > 0 && tokens[tokens.length - 1].type === 'text-input') {
          tokens[tokens.length - 1].ref.current?.focus();
        }
      }
    }
    if (e.nativeEvent.key === EVENT_KEYS.enter) {
      const tokens = await getUpdatedInputsAsync();
      updateCounterFromTokens(tokens);
      setTokens(tokens);
    }
  },
  removeToken: (index, type) => {
    const {tokens, defaultInputRef, updateCounterFromTokens} = get();
    const newTokens = tokens.filter((_, i) => i !== index);
    set({tokens: newTokens});
    if (newTokens.length === 0) {
      defaultInputRef.current?.focus();
    } else if (
      index > 0 &&
      (newTokens[index - 1]?.type === 'text-input' ||
        newTokens[index - 1]?.type === 'image-file' ||
        newTokens[index - 1]?.type === 'image-url' ||
        newTokens[index - 1]?.type === 'audio-file' ||
        newTokens[index - 1]?.type === 'audio-url')
    ) {
      newTokens[index - 1]?.ref?.current?.focus();
    }
    updateCounterFromTokens(newTokens);
  },
  updateToken: (index, value) => {
    const {tokens, updateCounterFromTokens} = get();
    const newTokens = [...tokens];
    if (newTokens[index] && newTokens[index].type === 'text-input') {
      newTokens[index].value = value;
    }
    set({tokens: newTokens});
    updateCounterFromTokens(newTokens);
  },
  handleImagePick: async () => {
    const {tokens, currentFocusedIndex, defaultInputRef, setTokens, updateCounterFromTokens} = get();
    const res = await launchImageLibrary({
      mediaType: 'photo',
      includeBase64: false,
    });

    const asset = res?.assets?.[0];
    if (!asset?.uri) return;

    const newImageToken: Token = {
      type: 'image-file' as TypeEnum,
      value: asset?.uri,
      fileName: res?.assets?.[0]?.fileName,
      asset,
    };

    let newTokens: Tokens;
    if (currentFocusedIndex === -1) {
      newTokens = [...tokens, newImageToken];
    } else {
      const before = tokens.slice(0, currentFocusedIndex + 1);
      const after = tokens.slice(currentFocusedIndex + 1);
      newTokens = [...before, newImageToken, ...after];
    }

    set({tokens: newTokens});
    updateCounterFromTokens(newTokens);
    defaultInputRef.current?.focus();
  },
  handleAddAudio: async (payload: {filePath: string}) => {
    const {tokens, currentFocusedIndex, defaultInputRef, setTokens, updateCounterFromTokens} = get();

    const newAudioToken: Token = {
      type: 'audio-file',
      value: payload.filePath,
    };

    let newTokens: Tokens;

    newTokens = [...tokens, newAudioToken];

    setTokens(newTokens);
    updateCounterFromTokens(newTokens);
    defaultInputRef.current?.focus();
  },
  goToNextToken: currentIndex => {
    const {tokens, defaultInputRef, setCurrentFocusedIndex} = get();
    if (tokens.length === 0) return;

    for (let i = currentIndex + 1; i < tokens.length; i++) {
      if (tokens[i].type === 'text-input') {
        tokens[i]?.ref?.current?.focus();
        setCurrentFocusedIndex(i);
        return;
      }
    }
    defaultInputRef.current?.focus();
    setCurrentFocusedIndex(-1);
  },
  updateCounterFromTokens: tokensToCount => {
    let charCount = 0;
    let imageCount = 0;
    let audioCount = 0;

    for (const token of tokensToCount) {
      if (token.type === 'text-input') {
        const text = String(token.value);
        charCount += text.trim().length > 0 ? text.length : 1;
      }
      if (token.type === 'image-file' || token.type === 'image-url') {
        imageCount += 1;
        charCount += 1;
      }
      if (token.type === 'audio-file' || token.type === 'audio-url') {
        audioCount += 1;
        charCount += 1;
      }
    }

    set({counter: {charCount, imageCount, audioCount}});
  },
  chosenJournalTypeId: '',
  setChosenJournalTypeId: chosenJournalTypeId => set({chosenJournalTypeId}),
  performCleanups: () => {
    const {setTokens, setChosenJournalTypeId, setCurrentFocusedIndex, setInputValue, setTitle, updateCounterFromTokens} = get();
    setTokens([]);
    setTitle('');
    setChosenJournalTypeId('');
    setInputValue('');
    setCurrentFocusedIndex(-1);
    updateCounterFromTokens([]);
  },
}));
