import {notify} from '@context/Notifier/NotifierContext';
import {router} from '@navigation/refs/navigation-ref';
import {userDb} from '@packages/db/functions';
import useStitch from '@packages/useStitch';
import {SessionUser, SessionUserResponse} from '@packages/useStitch/types';
import {ApiResponse} from '@packages/useStitch/types/common-api-types';
import React, {createContext, ReactNode, useContext, useEffect, useState} from 'react';

type AuthenticatedState = {
  isAuthenticated: true;
  session: SessionUserResponse;
  isLoading: boolean;
  isInitialLoading?: boolean;
  handleVerifyOtpSuccess?: (data: ApiResponse<SessionUserResponse>) => void;
  refetchUserInfo?: () => Promise<void>;
};

type UnauthenticatedState = {
  isAuthenticated: false;
  session: null;
  isLoading: boolean;
  isInitialLoading?: boolean;
  handleVerifyOtpSuccess?: (data: ApiResponse<SessionUserResponse>) => void;
  refetchUserInfo?: () => void;
};

export type AuthStateType = AuthenticatedState | UnauthenticatedState;

const initialAuthValue: AuthStateType = {
  isAuthenticated: false,
  session: null,
  isLoading: false,
  isInitialLoading: false,
  handleVerifyOtpSuccess: () => {},
  refetchUserInfo: () => {},
};

const AuthContext = createContext<AuthStateType>(initialAuthValue);

export const AuthProvider: React.FC<{children: ReactNode}> = ({children}) => {
  const {mutateAsync: fetchUserInfo} = useStitch('me');
  const {mutateAsync} = useStitch('autoLogin', {disableDefaultNotifier: true});
  const [authState, setAuthState] = useState<AuthStateType>(initialAuthValue);

  const tryLocalDbLogin = async () => {
    try {
      const localUser = await userDb.getUser();
      if (localUser != null) {
        const {
          age,
          isSignupCompleted,
          id,
          avatar,
          bio,
          email,
          gender,
          location,
          playlistLink,
          idShowCurrentMoodOnProfile,
          totalFollowers,
          totalFollowing,
          totalHealCount,
          totalPostCount,
          username,
        } = localUser;
        setAuthState({
          isLoading: false,
          isAuthenticated: true,
          session: {
            accessToken: localUser.accessToken,
            email: localUser.email,
            refreshToken: localUser.refreshToken,
            isSignupCompleted: isSignupCompleted,
            user: {
              age: age,
              avatar,
              profilePercentage: 80,
              bio,
              email,
              gender: gender as any,
              location,
              playlistLink,
              showCurrentMoodOnProfile: idShowCurrentMoodOnProfile,
              totalFollowers,
              totalFollowing,
              totalHealCount,
              totalPostCount,
              id,
              username,
            },
            username: localUser.username,
          },
        });
        return {
          status: true,
          user: localUser,
        };
      }

      return {
        status: false,
        user: null,
      };
    } catch (error) {
      console.log('Auto login error local', error);
      return {
        status: false,
        user: null,
      };
    }
  };

  const autoLogin = async () => {
    // First fecthes from local db -> then sets immendailtey the state to as per db if not null then in bg fectehs from server if this auth token get error then logs out and clear the user info.
    // also if any db error or intilazation error then also redirects to login and as it is working.
    try {
      const {user, status} = await tryLocalDbLogin();
      if (!user) return;
      // Dont call api if user is not present in local db.
      const data = await mutateAsync({
        refreshToken: user?.refreshToken ?? '',
      });

      setAuthState({
        isAuthenticated: true,
        isLoading: false,
        isInitialLoading: false,
        session: {
          accessToken: data.data.accessToken,
          refreshToken: data.data.refreshToken,
          email: data.data.email,
          isSignupCompleted: data.data.isSignupCompleted ?? false,
          user: data.data.user,
          username: data.data.username,
        },
      });

      const extras = {
        accessToken: data.data.accessToken,
        refreshToken: data.data.refreshToken,
        isAuthenticated: true,
        isSignupCompleted: data.data.isSignupCompleted ?? false,
      };
      userDb.setUser(extras, data?.data?.user ?? undefined);

      if (!data.data.isSignupCompleted) {
        router.navigate('GetStarted');
      }
    } catch (error) {
      await userDb.deleteAllUsers();
      await userDb.logout();
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        session: null,
      });

      console.log('Auto login error', error);
    }
  };

  /**
   *
   * @param data
   */
  const handleVerifyOtpSuccess = (data: ApiResponse<SessionUserResponse>) => {
    // Handling manual Signup and Login success
    // First we need to set the auth state to true  and upsert the data to local db.
    const user = data?.data?.user;
    console.log('Hitted Handle Verify Otp Success', data.data);

    setAuthState({
      isAuthenticated: true,
      isLoading: false,
      session: {
        isSignupCompleted: data.data.isSignupCompleted ?? false,
        username: data.data.username,
        accessToken: data.data.accessToken,
        refreshToken: data.data.refreshToken,
        email: data.data.email,
        user: user,
      },
    });

    // Now we need to upsert the user to local db. and send the user to chat tab screen or complete signup details if not completed.
    // if (data.data.isSignupCompleted) {
    const extras = {
      accessToken: data.data.accessToken,
      refreshToken: data.data.refreshToken,
      isAuthenticated: true,
      isSignupCompleted: data.data.isSignupCompleted ?? false,
    };
    userDb.setUser(extras, user ?? undefined);
    // }
  };

  useEffect(() => {
    autoLogin();
  }, []);

  const updateUserInfoOnly = (user: SessionUser) => {
    try {
      // first update the state upfront and then upsert to local db
      const updatedAuthState = {
        ...authState,
        isLoading: false,
        isInitialLoading: false,
        session: {
          ...authState.session,
          isSignupCompleted: true,
          user: {
            ...authState.session?.user,
            id: user.id,
            age: user.age ?? '',
            avatar: user.avatar ?? '',
            bio: user.bio ?? '',
            email: user.email,
            gender: user.gender ?? '',
            location: user.location ?? '',
            playlistLink: user.playlistLink ?? '',
            profilePercentage: user.profilePercentage ?? 0,
            showCurrentMoodOnProfile: user.showCurrentMoodOnProfile ?? false,
            totalFollowers: user.totalFollowers ?? 0,
            totalFollowing: user.totalFollowing ?? 0,
            totalHealCount: user.totalHealCount ?? 0,
            totalPostCount: user.totalPostCount ?? 0,
            username: user.username ?? '',
          },
        },
        isAuthenticated: true,
      } as AuthStateType;
      setAuthState(updatedAuthState);

      // Upsert to local db
      const session = authState.session;
      const extras = {
        accessToken: session!.accessToken,
        refreshToken: session!.refreshToken,
        isAuthenticated: true,
        isSignupCompleted: true,
      };
      userDb.setUser(extras, user);
    } catch (error) {
      console.log('Error >>', error);
    }
  };

  const refetchUserInfo = async () => {
    try {
      const res = await fetchUserInfo();
      if (res.data.username) {
        updateUserInfoOnly(res.data);
        notify.bottom('Userinfo updated successfully.');
      }
    } catch (error) {
      console.log('Unable to update user info');
    }
  };

  return (
    <AuthContext.Provider
      value={{
        ...authState,
        handleVerifyOtpSuccess,
        refetchUserInfo,
      }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthStateType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
