import { Router } from "express";
import { JournalController } from "./controller.js";
import { createJournalCategoriesRouter } from "./categories/router.js";

export function createJournalRouter() {
  const router = Router();
  router.post("/add", JournalController.createJournal);
  router.get("/list", JournalController.getJournals);
  router.post("/update/:id", JournalController.update);
  router.post("/delete/:id", JournalController.delete);
  router.use("/category", createJournalCategoriesRouter());
  router.get("/:id", JournalController.getJournalById);
  return router;
}
