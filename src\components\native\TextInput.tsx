import { ThemeColorKeys } from '@/types/color-types';
import { useTheme } from '@context/index';
import React, { useEffect } from 'react';
import { Control, Controller, FieldValues, Path, RegisterOptions } from 'react-hook-form';
import { NativeSyntheticEvent, Pressable, TextInput as RNTextInput, TextInputProps as RNTextInputProps, StyleSheet, Text, TextInputFocusEventData, View, ViewStyle } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Feather from 'react-native-vector-icons/Feather';
import { Text as NText, View as NView } from '../native';

export interface FloatingTextInputProps<T extends FieldValues> extends Omit<RNTextInputProps, 'value' | 'onChangeText'> {
  label: string;
  gapBottom?: number;
  suffixIcon?: React.ReactNode;
  prefixIcon?: React.ReactNode;
  onSuffixIconPress?: (setValue: (value: string) => void) => void;
  suffixIconAlwaysVisible?: boolean;
  isPassword?: boolean;
  containerStyles?: ViewStyle;
  inputPaddingTop?: number;
  inputPaddingBottom?: number;
  control: Control<T>;
  name: Path<T>;
  rules?: RegisterOptions<T>;
  labelType?: 'animated' | 'background' | 'outer-left';
  br?: number;
  bc?: ThemeColorKeys;
  bg?: ThemeColorKeys;
}

export const TextInput = React.forwardRef<RNTextInput, FloatingTextInputProps<any>>(
  (
    {
      label,
      containerStyles,
      labelType = 'animated',
      control,
      name,
      rules,
      suffixIcon,
      prefixIcon,
      isPassword,
      gapBottom = 12,
      style,
      onFocus,
      bc = 'transparent',
      br = 16,
      bg,
      onBlur,
      inputPaddingTop,
      inputPaddingBottom,
      onSuffixIconPress,
      suffixIconAlwaysVisible = false,
      ...props
    },
    ref,
  ) => {
    const { colors } = useTheme();
    const isFocused = useSharedValue(false);
    const inputValue = useSharedValue('');

    const [isPasswordVisible, setPasswordVisible] = React.useState(false);

    const handleFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
      isFocused.value = true;
      onFocus?.(e);
    };

    const handleBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>, onControllerBlur: () => void) => {
      isFocused.value = false;
      onBlur?.(e);
      onControllerBlur();
    };

    const animatedContainerStyle = useAnimatedStyle(() => ({
      borderColor: isFocused.value ? colors.neutral20 : bc,
    }));

    const animatedLabelStyle = useAnimatedStyle(() => {
      const shouldFloat = isFocused.value || inputValue.value.length > 0;
      return {
        top: withTiming(shouldFloat ? 8 : 20, { duration: 200 }),
        fontSize: withTiming(shouldFloat ? 12 : 14, { duration: 200 }),
      };
    });

    const animatedIconStyle = useAnimatedStyle(() => ({
      display: isFocused.value ? 'flex' : 'none',
    }));

    const renderSuffixIcon = (value: string, setValue: (value: string) => void) => {
      if (suffixIcon) return suffixIcon;

      if (isPassword) {
        return (
          <Pressable onPress={() => setPasswordVisible(prev => !prev)}>
            <Feather name={isPasswordVisible ? 'eye' : 'eye-off'} color="#fff" size={20} />
          </Pressable>
        );
      }

      return value ? (
        <Pressable onPress={() => onSuffixIconPress?.(setValue)}>
          <EvilIcons color={colors.neutral50} size={30} name="close" />
        </Pressable>
      ) : null;
    };
    const renderPrefixIcon = () => {
      if (prefixIcon) return prefixIcon;
    };

    return (
      <Controller
        control={control}
        name={name}
        rules={rules}
        render={({ field: { value = '', onChange, onBlur: onControllerBlur }, fieldState: { error } }) => {
          useEffect(() => {
            inputValue.value = value ?? '';
          }, [value]);

          return (
            <>
              {labelType == 'outer-left' && (
                <NView ml={2} mb={3}>
                  <NText fs="12" fw="500" color="purple600">
                    {label}
                  </NText>
                </NView>
              )}
              <Animated.View style={[styles.container, animatedContainerStyle, { backgroundColor: colors[bg ?? 'neutral00'], borderRadius: br }, containerStyles]}>
                {labelType == 'animated' && <Animated.Text style={[styles.label, animatedLabelStyle, { color: colors.neutral70 }]}>{label}</Animated.Text>}
                <View style={styles.inputWrapper}>
                  {prefixIcon && <Animated.View style={[styles.prefixIcon]}>{renderPrefixIcon()}</Animated.View>}
                  <RNTextInput
                    ref={ref}
                    value={value}
                    onChangeText={onChange}
                    secureTextEntry={isPassword && !isPasswordVisible}
                    style={[
                      {
                        paddingTop: inputPaddingTop ?? labelType == 'animated' ? 26 : 20,
                        paddingBottom: inputPaddingBottom ?? labelType == 'animated' ? 0 : 6,
                        flex: 1,
                        color: colors.neutral80,
                        ...(inputPaddingTop != undefined && { paddingTop: inputPaddingTop }),
                        ...(inputPaddingBottom != undefined && { paddingBottom: inputPaddingBottom }),
                      },
                      styles.input,
                      style,
                    ]}
                    onFocus={handleFocus}
                    onBlur={e => handleBlur(e, onControllerBlur)}
                    selectionColor={colors.purple700}
                    placeholder={labelType == 'background' ? label : ''}
                    placeholderTextColor={colors.neutral30}
                    {...props}
                  />
                  <Animated.View style={[suffixIconAlwaysVisible ? {} : animatedIconStyle, styles.suffixIcon]}>{renderSuffixIcon(value, onChange)}</Animated.View>
                </View>
              </Animated.View>
              {error && <Text style={[styles.error, { color: colors.negative50 }]}>{error.message}</Text>}
              {gapBottom ? <View style={{ height: gapBottom }} /> : null}
            </>
          );
        }}
      />
    );
  },
);

TextInput.displayName = 'TextInput';

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingTop: 0,
    paddingBottom: 10,
    position: 'relative',
    borderWidth: 1,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    fontSize: 16,
    padding: 0,
    paddingLeft: 5,
    margin: 0,
  },
  label: {
    position: 'absolute',
    left: 12,
    paddingHorizontal: 4,
  },
  suffixIcon: {
    marginLeft: 8,
    height: '100%',
    paddingTop: 16,
  },
  prefixIcon: {
    marginRight: 8,
    height: '100%',
    paddingTop: 16,
  },
  error: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
    marginLeft: 12,
  },
});

export const AnimatedTextInput = Animated.createAnimatedComponent(TextInput);

AnimatedTextInput.displayName = 'AnimatedTextInput';
