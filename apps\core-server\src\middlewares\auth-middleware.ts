import { HashingModule } from "@/lib/hashing-module.js";
import { paramsValidator, validateId } from "@/lib/validation-module.js";
import db from "@repo/db";
import { users } from "@repo/db/schema";
import { TokenModule } from "@repo/lib";
import { eq } from "drizzle-orm";
import type { Request, Response, NextFunction } from "express";
export class AuthMiddlewares {
  static validateAccessToken(req: Request, res: Response, next: NextFunction) {
    try {
      const accessToken = req.headers["authorization"]?.split(" ")[1];
      if (!accessToken) {
        res.status(401).json({ message: "access token missing" });
        return;
      }

      const payload = TokenModule.verifyAccessToken(accessToken);

      if (!payload) {
        res.status(401).json({ message: "invalid access token" });
        return;
      }
      req.user = {
        id: payload.userID,
        email: payload.email,
      };
      return next();
    } catch (error) {
      next(error);
    }
  }

  static async onlyAdmin(req: Request, res: Response, next: NextFunction) {
    try {
      const accessToken = req.headers["authorization"]?.split(" ")[1];
      if (!accessToken) {
        res.status(401).json({ message: "access token missing" });
        return;
      }

      const payload = TokenModule.verifyAccessToken(accessToken);

      if (!payload) {
        res.status(401).json({ message: "invalid access token" });
        return;
      }

      const [user] = await db
        .select({ role: users.role })
        .from(users)
        .where(eq(users.id, payload.userID));

      if (user?.role !== "admin") {
        res.status(401).json({ message: "unauthorized" });
        return;
      }
      req.user = {
        id: payload.userID,
        email: payload.email,
      };
      return next();
    } catch (error) {
      next(error);
    }
  }
}
