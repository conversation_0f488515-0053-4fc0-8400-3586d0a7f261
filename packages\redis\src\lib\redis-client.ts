import { Redis } from "ioredis";

export const redisClient = new Redis({
  host: process.env.REDIS_HOST,
  port: Number(process.env.REDIS_PORT),
  username: process.env.REDIS_USERNAME,
  password: process.env.REDIS_PASSWORD,
});

function setEventListners() {
  redisClient.on("error", (err: any) => {
    console.log("Could not establish a connection with redis. " + err);
  });
  redisClient.on("connect", (err: any) => {
    if (err) {
      console.log("Could not establish a connection with redis. " + err);
    }
    console.log("Connected to redis successfully");
  });
}

setEventListners();

// export default redisClient;
