import {ThemeColors} from '@/types/color-types';
import Wave from '@assets/svgs/audio-wave.svg';
import MicIcon from '@assets/svgs/mic-icon.svg';
import {AnimatedText, Text, View} from '@components/native';
import {BounceTap, Ripple} from '@components/shared/animated';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {ScrollView} from 'react-native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import {useAnimatedProps, useSharedValue} from 'react-native-reanimated';
import FoundationIcon from 'react-native-vector-icons/Foundation';

type MicSheetComponentProps = {
  closeSheet: () => void;
  openSheet: () => void;
  colors: ThemeColors;
  onDone?: (payload: {filePath: string; duration?: number}) => void;
};

const MAX_FREQ_HEIGHT = 40;

const WaveDisplay: React.FC<{frequencies: number[]}> = React.memo(({frequencies}) => (
  <View flex={1}>
    <ScrollView horizontal>
      {frequencies.map((value, index) => (
        <View mr={0.2} key={index} flexCenterColumn h={MAX_FREQ_HEIGHT}>
          <Wave height={MAX_FREQ_HEIGHT * value} />
        </View>
      ))}
    </ScrollView>
  </View>
));

const RecordingTimer: React.FC<{children: any}> = React.memo(({children}) => (
  <AnimatedText fs="24" color="purple500" fw="700" ta="center" my={14}>
    {children}
  </AnimatedText>
));

const RecordingControls: React.FC<{
  recordingState: 'played' | 'paused';
  pauseRecording: () => void;
  resumeRecording: () => void;
  colors: ThemeColors;
}> = React.memo(({recordingState, pauseRecording, resumeRecording, colors}) => (
  <BounceTap onPress={recordingState === 'played' ? pauseRecording : resumeRecording}>
    <View bw={1} bc="orange" br={100} flexCenterRow size={55}>
      {recordingState === 'played' ? <View size={21} bg="negative50" br={3} /> : <FoundationIcon size={30} name="play" color={colors.negative50} />}
    </View>
  </BounceTap>
));

export const MicSheetComponent: React.FC<MicSheetComponentProps> = ({closeSheet, colors, openSheet, onDone}) => {
  const audioRecorderPlayer = useMemo(() => new AudioRecorderPlayer(), []);
  const [isRecording, setIsRecording] = useState(false);
  const [frequencies, setFrequencies] = useState<number[]>([]);
  const [recordingState, setRecordingState] = useState<'played' | 'paused'>('played');
  const [filePath, setFilePath] = useState<string | null>(null);
  const recordTime = useSharedValue(0);
  const formattedRecordTime = useSharedValue('0:00');
  const recordListenerRef = useRef<any>(null);

  useEffect(() => {
    return () => {
      audioRecorderPlayer.stopPlayer();
      audioRecorderPlayer.removePlayBackListener();
      audioRecorderPlayer.stopRecorder();
      audioRecorderPlayer.removeRecordBackListener();
    };
  }, [audioRecorderPlayer]);

  const formatTime = useCallback((milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  }, []);

  const startRecording = useCallback(async () => {
    setIsRecording(true);
    const result = await audioRecorderPlayer.startRecorder();
    setFilePath(result);
    recordListenerRef.current = audioRecorderPlayer.addRecordBackListener((e: {currentPosition: number}) => {
      const currentPosIndex = Number(Math.random().toFixed(1));
      setFrequencies(prev => [...prev, currentPosIndex]);
      recordTime.value = e.currentPosition;
      formattedRecordTime.value = formatTime(e.currentPosition);
    });
  }, [audioRecorderPlayer, recordTime, formattedRecordTime, formatTime]);

  const stopRecording = useCallback(async () => {
    const result = await audioRecorderPlayer.stopRecorder();
    audioRecorderPlayer.removeRecordBackListener();
    setIsRecording(false);
    setFilePath(result);
    recordTime.value = 0;
    formattedRecordTime.value = '0:00';
  }, [audioRecorderPlayer, recordTime, formattedRecordTime]);

  const pauseRecording = useCallback(async () => {
    await audioRecorderPlayer.pauseRecorder();
    setRecordingState('paused');
  }, [audioRecorderPlayer]);

  const resumeRecording = useCallback(async () => {
    await audioRecorderPlayer.resumeRecorder();
    setRecordingState('played');
  }, [audioRecorderPlayer]);

  const handleDone = useCallback(async () => {
    if (isRecording) {
      await stopRecording();
    }
    if (filePath && onDone) {
      onDone({filePath, duration: recordTime.value});
    }
    setFrequencies([]);
    recordTime.value = 0;
    formattedRecordTime.value = '0:00';
    setFilePath(null);
    closeSheet();
  }, [isRecording, filePath, onDone, closeSheet, stopRecording, recordTime, formattedRecordTime]);

  const animatedProps = useAnimatedProps(() => ({
    children: formattedRecordTime.value,
  }));

  const InitialRecordScreen = useMemo(
    () => (
      <View flexCenterColumn mih={200}>
        <BounceTap onPress={startRecording}>
          <View size={65} br={100} bg="purple500" flexCenterRow>
            <MicIcon stroke="white" />
          </View>
        </BounceTap>
        <Text fs="12" color="neutral80" fw="500" my={14}>
          Tap to record audio
        </Text>
        <BounceTap onPress={closeSheet}>
          <Text tdl="underline" mt={20} color="negative50" fw="700">
            Cancel
          </Text>
        </BounceTap>
      </View>
    ),
    [closeSheet, startRecording],
  );

  const RecordingHeader = useMemo(
    () => (
      <View display="flex" fd="row" ai="center" jc="space-between">
        <Text color="neutral80" fs="14" fw="600">
          Recording voice
        </Text>
        <Ripple onPress={handleDone}>
          <View flexCenterRow px={10} py={5}>
            <Text fs="12" color="purple500" fw="700">
              ADD
            </Text>
          </View>
        </Ripple>
      </View>
    ),
    [handleDone],
  );

  const OnRecordingScreen = useMemo(
    () => (
      <View p={20}>
        {RecordingHeader}
        <RecordingTimer>{formattedRecordTime.value.toString()}</RecordingTimer>
        <View p={10} gap={10} display="flex" fd="row">
          <WaveDisplay frequencies={frequencies} />
          <RecordingControls recordingState={recordingState} pauseRecording={pauseRecording} resumeRecording={resumeRecording} colors={colors} />
        </View>
      </View>
    ),
    [frequencies, recordingState, pauseRecording, resumeRecording, colors, animatedProps],
  );

  return isRecording ? OnRecordingScreen : InitialRecordScreen;
};
