import React, {useEffect, useRef, useState} from 'react';
import {Animated, PermissionsAndroid, Platform, Text, TouchableOpacity, View} from 'react-native';
import AudioRecorderPlayer, {RecordBackType} from 'react-native-audio-recorder-player';

export const AudioScreen = () => {
  const audioRecorderPlayer = new AudioRecorderPlayer();
  audioRecorderPlayer.setSubscriptionDuration(0.5);
  const [recording, setRecording] = useState(false);
  const [metering, setMetering] = useState(0);
  const [filePath, setFilePath] = useState<string | null>(null);
  const waveValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (recording) {
      audioRecorderPlayer.addRecordBackListener((e: RecordBackType) => {
        if (e.currentMetering != null) {
          setMetering(e.currentMetering);
          Animated.spring(waveValue, {
            toValue: e.currentMetering * 100,
            useNativeDriver: false,
          }).start();
        }
        return;
      });
    }

    return () => {
      audioRecorderPlayer.removeRecordBackListener();
    };
  }, [recording]);

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      const grants = await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
      ]);

      return (
        grants['android.permission.RECORD_AUDIO'] === PermissionsAndroid.RESULTS.GRANTED &&
        grants['android.permission.WRITE_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED &&
        grants['android.permission.READ_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED
      );
    }
    return true;
  };

  const startRecording = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    const uri = await audioRecorderPlayer.startRecorder(undefined, undefined, true);
    setFilePath(uri);
    setRecording(true);
  };

  const stopRecording = async () => {
    const result = await audioRecorderPlayer.stopRecorder();
    audioRecorderPlayer.removeRecordBackListener();
    setRecording(false);
    setFilePath(result);
  };

  const playRecording = async () => {
    if (!filePath) return;
    await audioRecorderPlayer.startPlayer(filePath);
  };

  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <TouchableOpacity onPress={recording ? stopRecording : startRecording}>
        {recording ? (
          <View style={{width: 50, height: 50, backgroundColor: 'red', borderRadius: 10}} />
        ) : (
          <View style={{width: 50, height: 50, borderWidth: 2, borderColor: 'blue', borderRadius: 25}} />
        )}
      </TouchableOpacity>

      <Animated.View
        style={{
          width: 20,
          height: waveValue,
          backgroundColor: 'green',
          marginTop: 20,
        }}
      />

      {filePath && (
        <View style={{marginTop: 20}}>
          <Text>Recording saved at: {filePath}</Text>
          <TouchableOpacity
            onPress={playRecording}
            style={{marginTop: 10}}>
            <Text>Play Recording</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
