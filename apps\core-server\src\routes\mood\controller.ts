import { Infer } from "@vinejs/vine/types";
import type { Request, Response, NextFunction } from "express";
import { logMoodValidator } from "./validator.js";
import { bodyValidator } from "@/lib/validation-module.js";
import db from "@repo/db";
import { moodTable } from "@repo/db/schema";
import { gte, lte, sql } from "drizzle-orm";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc.js";
dayjs.extend(utc);

export class MoodController {
  @bodyValidator(logMoodValidator)
  static async logMood(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof logMoodValidator>;
      await db.insert(moodTable).values({
        value: payload.mood,
        userId: req.user.id,
        timestamp: dayjs(payload.timestamp).utc().toDate(),
      });
      res.json({ message: "Mo<PERSON> logged", success: true });
      return;
    } catch (error) {
      next(error);
    }
  }
  static async getWeekMoodLog(req: Request, res: Response, next: NextFunction) {
    try {
      const { rows } = (await db.execute(sql`
            SELECT TO_CHAR(${moodTable.timestamp}, 'Dy') AS weekday, 
            JSON_AGG(
              JSON_BUILD_OBJECT(
                'value', ${moodTable.value},
                'timestamp', ${moodTable.timestamp}
                )
              ) AS logs 
            FROM ${moodTable}
            WHERE ${moodTable.timestamp} >= NOW() - INTERVAL '7 days'
            GROUP BY TO_CHAR(${moodTable.timestamp}, 'Dy')
            ORDER BY MAX(${moodTable.timestamp}) DESC;
          `)) satisfies {
        rows: { weekday: string; logs: { value: string; timestamp: Date }[] }[];
      };
      res.json({ data: rows });
    } catch (error) {
      next(error);
    }
  }
  static async getCalender(req: Request, res: Response, next: NextFunction) {
    try {
      // TODO: get the last log of the day for
      // const { rows } = await db.execute(sql`
      //    SELECT value, TO_CHAR(timestamp, 'MM-DD-YYYY') AS date
      //   FROM (
      //     SELECT value, timestamp, ROW_NUMBER() OVER (PARTITION BY TO_CHAR(timestamp, 'M-DD-YYYYM') ORDER BY timestamp DESC) AS rn
      //     FROM mood_table
      //   ) sub
      //   WHERE rn = 1
      //   `);

      const sq = db.$with("sq").as(
        db
          .select({
            value: moodTable.value,
            timestamp: moodTable.timestamp,
            row_number: sql<number>`ROW_NUMBER() OVER (
            PARTITION BY TO_CHAR(${moodTable.timestamp}, 'MM-DD-YYYY')
            ORDER BY ${moodTable.timestamp} DESC 
            )`,
          })
          .from(moodTable),
      );

      const rows = await db
        .with(sq)
        .select({
          value: sql<string>`value`,
          date: sql<string>`TO_CHAR(timestamp, 'MM-DD-YYYY')`,
        })
        .from(sq)
        .where(sql`row_number = 1`);

      res.json({ data: rows });
    } catch (error) {
      next(error);
    }
  }
}
