import {
  json,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  index,
} from "drizzle-orm/pg-core";
import { mediaTypeEnum, users } from "./common.js";

export type Attachment = {
  type: (typeof mediaTypeEnum.enumValues)[number];
  url: string;
  preview: string;
};

export const conversationTypeEnum = pgEnum("conversation_type_enum", [
  "personal",
  "booking",
]);

export const chatMessageTable = pgTable("chat_messages", {
  id: uuid("id").primaryKey().defaultRandom(),
  roomId: uuid("room_id")
    .references(() => chatRoomTable.id, { onDelete: "cascade" })
    .notNull(),
  senderId: uuid("sender_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  message: text("message").notNull(),

  attachments: json("attachments").$type<Attachment>().array(),
  sentAt: timestamp("sent_at").defaultNow(),
  linkPreview: json("link_preview"),
  deliveredAt: timestamp("delivered_at"),
  seenAt: timestamp("seen_at"),
});

// Code to add index !! Will implement later to check the query time comparison.
//   seenAt: timestamp("seen_at"),
// },(table)=>[
//   index("roomId_idx").on(table.roomId),
// ]);

export const chatRoomTable = pgTable("chat_rooms", {
  id: uuid("id").primaryKey().defaultRandom(),
  user1: uuid("user1")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  user2: uuid("user2")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  listingId: uuid("listing_id"),
  blockedBy: uuid("blocked_by").references(() => users.id, {
    onDelete: "set null",
  }),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date()),
});
