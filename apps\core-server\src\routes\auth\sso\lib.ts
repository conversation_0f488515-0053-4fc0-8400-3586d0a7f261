import { OAuth2Client } from "google-auth-library";
import { JwksClient } from "jwks-rsa";
import jwt from "jsonwebtoken";

const client = new OAuth2Client({
  clientId:
    "308400612476-p3bcodana7b5gbsmqi1or4h268otaa6b.apps.googleusercontent.com",
});

export const GoogleAuthValidator = {
  async validateIdToken(token: string) {
    try {
      const ticket = await client.verifyIdToken({
        idToken: token,
      });
      const payload = ticket.getPayload();
      if (!payload) throw new Error("Invalid token");
      // TODO: check for audience
      return {
        email: (payload.email as string)?.toLowerCase(),
        firstName: payload.given_name,
        lastName: payload.family_name,
        picture: payload.picture,
      };
    } catch (error) {
      throw error;
    }
  },
};

export const AppleAuthValidator = {
  client: new JwksClient({
    jwksUri: "https://appleid.apple.com/auth/keys",
    cache: true,
    rateLimit: true,
  }),
  validateIdToken: async function (token: string) {
    try {
      const decoded = jwt.decode(token, { complete: true });
      if (!decoded || typeof decoded === "string") {
        throw new Error("Invalid token format");
      }
      const kid = decoded.header.kid;
      if (!kid) throw new Error("No key Id found in token header");
      const publicKey = (await this.client.getSigningKey(kid)).getPublicKey();
      const payload = jwt.verify(token, publicKey, {
        algorithms: ["RS256"],
        issuer: "https://appleid.apple.com",
      }) as jwt.JwtPayload;
      //TODO: Check for audiences
      return {
        email: (payload.email as string)?.toLowerCase(),
        firstName: payload.given_name,
        lastName: payload.family_name,
        picture: payload.picture,
      };
    } catch (error) {
      throw error;
    }
  },
};
