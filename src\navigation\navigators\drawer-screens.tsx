import AboutIcon from '@assets/svgs/about-icon.svg';
import BookmarkIcon from '@assets/svgs/bookmark-icon.svg';
import FaqsIcon from '@assets/svgs/faq-icon.svg';
import LogoutIcon from '@assets/svgs/logout-icon.svg';
import PrivacyPolicyIcon from '@assets/svgs/privacy-policy-icon.svg';
import QuestionaireIcon from '@assets/svgs/questionaire-icon.svg';
import SettingsIcon from '@assets/svgs/settings-icon.svg';
import TermsOfServiceIcon from '@assets/svgs/terms-of-service-icon.svg';
import {Pressable, Text, View} from '@components/native';
import {Avatar} from '@components/shared';
import {Ripple} from '@components/shared/animated';
import {router} from '@navigation/refs/navigation-ref';
import {createDrawerNavigator, DrawerContentComponentProps, useDrawerStatus} from '@react-navigation/drawer';
import {Dimensions, Image, ScrollView} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AuthenticatedTabs from './auth-tabs';
const Drawer = createDrawerNavigator();
const {width} = Dimensions.get('screen');

const DrawerScreens = () => {
  return (
    <Drawer.Navigator
      drawerContent={props => <CustomDrawerComponent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          maxWidth: width * 0.8,
          width: width * 0.8,
        },
      }}>
      <Drawer.Screen name="tab" component={AuthenticatedTabs} options={{title: 'Home'}} />
    </Drawer.Navigator>
  );
};

export default DrawerScreens;

const DrawerHeader = () => {
  const DRAWER_HEADER_WIDTH = width * 0.8;
  const DRAWER_HEADER_HEIGHT = 200;
  const isOpened = useDrawerStatus() == 'open';
  const {top} = useSafeAreaInsets();

  return (
    <>
      {isOpened && (
        <View disableSizeMatter>
          <Image
            style={{width: DRAWER_HEADER_WIDTH, height: DRAWER_HEADER_HEIGHT, borderBottomLeftRadius: 20, borderBottomRightRadius: 20}}
            source={require('@assets/images/drawer-bg.png')}
          />
          <View disableSizeMatter pos="absolute" top={0} pt={top} display="flex" jc="flex-end" px={20} pb={20} h={DRAWER_HEADER_HEIGHT} w={DRAWER_HEADER_WIDTH} left={0}>
            <Pressable onPress={router.closeDrawer} pos="absolute" top={top + 20} right={20}>
              <Ionicons size={22} name="close" />
            </Pressable>
            <View display="flex" fd="row" ai="center">
              <Avatar showProgressRing={false} />
              <View gap={5} px={10}>
                <Text color="neutral80" fs="12">
                  Lucas Benjamin Scott
                </Text>
                <Text fs="12" color="neutral80" fw="600">
                  @luca_b_2025
                </Text>
              </View>
            </View>
          </View>
        </View>
      )}
    </>
  );
};

type DrawerItemProps = {
  onPress?: () => void;
  icon: React.ReactElement;
  title: string;
  noBorder?: boolean;
};
const DrawerItem: React.FC<DrawerItemProps> = ({onPress, title, icon, noBorder = false}) => {
  return (
    <Ripple onPress={onPress} style={{paddingTop: 10, marginHorizontal: 10, display: 'flex', flexDirection: 'column', justifyContent: 'space-between'}}>
      <View ai="center" gap={16} display="flex" fd="row">
        <View miw={16}>{icon}</View>
        <Text color="neutral70" fw="500" fs="14">
          {title}
        </Text>
      </View>
      {!noBorder && <View mt={10} h={0.5} bg="neutral10" />}
    </Ripple>
  );
};

const CustomDrawerComponent = ({...props}: DrawerContentComponentProps) => {
  return (
    <View display="flex" fd="column" jc="space-between" style={{height: '100%'}}>
      <DrawerHeader />
      <View flex={1} pt={32} px={15}>
        <ScrollView bounces={false}>
          <View gap={12}>
            <DrawerItem onPress={() => router.navigate('Bookmarks')} title="Update Questionnaire" icon={<QuestionaireIcon />} />
            <DrawerItem onPress={() => router.navigate('Bookmarks')} title="Bookmark" icon={<BookmarkIcon />} />
            <DrawerItem onPress={() => router.navigate('Faqs')} title="FAQs" icon={<FaqsIcon />} />
            <DrawerItem onPress={() => router.navigate('Bookmarks')} title="About" icon={<AboutIcon />} />
            <DrawerItem onPress={() => router.navigate('Settings')} title="Settings" icon={<SettingsIcon />} />
          </View>
        </ScrollView>
      </View>
      <DrawerFooter />
    </View>
  );
};

const DrawerFooter = () => {
  const {bottom} = useSafeAreaInsets();

  return (
    <View pt={20} pb={bottom + 10} gap={20} mx={20} bc="neutral10" style={{borderTopWidth: 1}}>
      <DrawerItem icon={<LogoutIcon />} onPress={() => router.navigate('Login')} title="Logout" noBorder />
      <DrawerItem icon={<PrivacyPolicyIcon />} onPress={() => router.navigate('Login')} title="Privacy Policy" noBorder />
      <DrawerItem icon={<TermsOfServiceIcon />} onPress={() => router.navigate('Login')} noBorder title="Terms of Services" />
    </View>
  );
};
