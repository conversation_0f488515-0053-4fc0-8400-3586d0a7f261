import type { Request, Response, NextFunction } from "express";
import {
  createCommentValidator,
  editCommentValidator,
  getCommentsValidator,
} from "./validator.js";
import { bodyValidator, queryValidator } from "@/lib/validation-module.js";
import { Infer } from "@vinejs/vine/types";
import db from "@repo/db";
import { commentsTable, userDetails } from "@repo/db/schema";
import { and, count, desc, eq, isNull } from "drizzle-orm";
import { Paginator } from "@/lib/pagination-module.js";
export class CommentsController {
  @bodyValidator(createCommentValidator)
  static async createComment(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof createCommentValidator>;

      await db.insert(commentsTable).values({
        userId: req.user.id,
        postId: payload.postId,
        text: payload.text,
        replyTo: payload.replyTo,
      });
      res.json({ message: "Comment created", success: true });
    } catch (error) {
      next(error);
    }
  }

  static async deleteComment(req: Request, res: Response, next: NextFunction) {
    try {
      const commentId = req.params.id as string;
      await db
        .delete(commentsTable)
        // .set({ deletedAt: new Date() })
        .where(eq(commentsTable.id, commentId));
      res.json({ message: "Comment deleted successfully" });
    } catch (error) {
      next(error);
    }
  }
  @queryValidator(getCommentsValidator)
  static async getComments(req: Request, res: Response, next: NextFunction) {
    try {
      const query = req.query as Infer<typeof getCommentsValidator>;
      const { limit, offset } = Paginator.getPage(query);
      const filters = [isNull(commentsTable.deletedAt)];
      if (query.postId) {
        filters.push(eq(commentsTable.postId, query.postId));
      }
      if (query.replyTo) {
        filters.push(eq(commentsTable.replyTo, query.replyTo));
      }
      const filter = and(...filters);
      const total = await Paginator.getTotalCount(
        db.select({ count: count() }).from(commentsTable).where(filter),
      );

      const comments = await db
        .select({
          id: commentsTable.id,
          text: commentsTable.text,
          createdAt: commentsTable.createdAt,
          user: {
            id: userDetails.id,
            username: userDetails.userName,
            avatar: userDetails.avatarUrl,
          },
        })
        .from(commentsTable)
        .leftJoin(userDetails, eq(userDetails.userId, commentsTable.userId))
        .where(filter)
        .groupBy(commentsTable.id, userDetails.id)
        .orderBy(desc(commentsTable.updatedAt))
        .limit(limit)
        .offset(offset);

      const data = Paginator.paginate(req.query, comments, total);
      res.json({ data });
    } catch (error) {
      next(error);
    }
  }

  @bodyValidator(editCommentValidator)
  static async editComment(req: Request, res: Response, next: NextFunction) {
    try {
      const commentId = req.params.id as string;
      const payload = req.body as Infer<typeof editCommentValidator>;
      await db
        .update(commentsTable)
        .set({ text: payload.text })
        .where(eq(commentsTable.id, commentId));
      res.json({ message: "Comment edited successfully" });
    } catch (error) {
      next(error);
    }
  }
}
