{"name": "@repo/email", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "email dev --port 4625", "build": "tsc --project tsconfig.json && tsc-alias -p tsconfig.json"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^20", "@types/react": "^19.0.0", "tsc-alias": "^1.8.10", "@types/react-dom": "^18.3.1", "react-email": "3.0.4"}, "exports": {".": {"production": {"import": "./dist/src/index.js"}, "default": "./src/index.ts"}, "./env": {"production": {"import": "./dist/env.js"}, "default": "./src/env.ts"}}, "dependencies": {"@react-email/components": "0.0.31", "@react-email/render": "1.0.5", "@react-email/tailwind": "1.0.4", "@repo/typescript-config": "workspace:*", "react": "^19.0.0", "react-dom": "^19.0.0"}}