import db, { Dr<PERSON><PERSON>D<PERSON>, Dr<PERSON>zleTransaction } from "@repo/db";
import { RepoPayload } from "./types.js";
import { addressTable, userDetails, users } from "@repo/db/schema";
import { and, eq, sql } from "drizzle-orm";
import { defaultAvatarUrl } from "@repo/lib";

type UserQueryPayload = {
  email?: string;
  userId?: string;
  refreshToken?: string;

  returnEmpty?: boolean;
};

export class UserRepo {
  static async getPersonalUser(payload: UserQueryPayload, tx: DrizzleTransaction | DrizzleDB = db) {
    const filters = [eq(users.role, "user")];
    if (payload.email) {
      filters.push(eq(users.email, payload.email));
    }

    if (payload.userId) {
      filters.push(eq(users.id, payload.userId));
    }
    if (payload.refreshToken) {
      filters.push(eq(users.refreshToken, payload.refreshToken));
    }

    const [user] = await tx
      .select({
        id: users.id,
        email: users.email,
        role: users.role,
        username: userDetails.userName,
        verifiedAt: users.verifiedAt,
        verified: users.verified,
        gender: userDetails.gender,
        bio: userDetails.bio,
        dob: userDetails.dob,
        visibility: userDetails.visibility,
        notifications: userDetails.notifications,
        avatar: userDetails.avatarUrl,
        address: {
          country: addressTable.country,
          city: addressTable.city,
          state: addressTable.state
        },
        location:
          sql`CONCAT_WS(', ', ${addressTable.city}, ${addressTable.state}, ${addressTable.country})`.as(
            "location"
          ),
        currentMood: userDetails.currentMood,
        showCurrentMoodOnProfile: userDetails.showCurrentMood,
        playlistLink: userDetails.playlistLink,
        followerCount: userDetails.followersCount,
        followingCount: userDetails.followingCount,
        totalPostCount: userDetails.totalPostCount,
        totalHealCount: userDetails.totalHealCount
      })
      .from(users)
      .where(and(...filters))
      .leftJoin(userDetails, eq(userDetails.userId, users.id))
      .leftJoin(addressTable, eq(addressTable.id, userDetails.addressId));

    if (!user && payload.returnEmpty) {
      return {
        id: "00000000-0000-0000-0000-000000000000",
        email: "",
        username: "",
        verifiedAt: null,
        verified: false,
        gender: "other",
        bio: "",
        visibility: false,
        notifications: false,
        avatar: defaultAvatarUrl,
        address: {
          country: "",
          city: "",
          state: ""
        },
        location: "",
        currentMood: "",
        showCurrentMoodOnProfile: false,
        playlistLink: "",
        followerCount: 0,
        followingCount: 0,
        totalPostCount: 0,
        totalHealCount: 0
      };
    }
    return user;
  }
}
