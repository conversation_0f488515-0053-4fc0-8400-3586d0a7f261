import AppleIcon from '@assets/svgs/apple-icon.svg';
import GoogleIcon from '@assets/svgs/google-icon.svg';
import {Button, Text, TextInput, View} from '@components/native';
import {StackBackButton} from '@components/shared';
import {BounceTap, StaggeredMotiFadeInAnim} from '@components/shared/animated';
import {router} from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import {SignupLoginMutationBody} from '@packages/useStitch/types';
import React from 'react';
import {SubmitHandler, useForm} from 'react-hook-form';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import AnimatedAuthPipeAnimationWrapper from './components/AnimatedAuthPipeAnimationWrapper';

export const SignupScreen = () => {
  const {top} = useSafeAreaInsets();
  const {isMutating, mutateAsync} = useStitch('signup', {
    mutationOptions: {
      onSuccess: (_, variables) => {
        router.navigate('VerifyOtp', {
          email: variables.email,
          type: 'signup',
        });
      },
      onError: error => {
        // Handle error, e.g., show a toast notification
        console.error('Signup error:', error);
      },
    },
  });

  const {control, handleSubmit} = useForm<SignupLoginMutationBody>({
    defaultValues: {
      email: '',
    },
  });

  const onSubmit: SubmitHandler<SignupLoginMutationBody> = data => {
    mutateAsync({email: data.email.toLowerCase().trim()});
  };

  return (
    <AnimatedAuthPipeAnimationWrapper>
      <View flex={1} p={20} display="flex" jc="space-evenly">
        {router.canGoBack() && <StackBackButton title="Sign up" />}
        <StaggeredMotiFadeInAnim>
          <View flexCenterColumn mt={top} gap={20}>
            <Text fw="500" color="neutral80" ta="center" ff="PlayfairDisplay-Medium" fs="26">
              Create an account to {'\n'} get stitched.
            </Text>
          </View>
        </StaggeredMotiFadeInAnim>

        <View>
          <TextInput
            control={control}
            name="email"
            label="Email"
            bg="neutral10"
            rules={{
              required: 'Email is required',
              pattern: {
                value: /^\S+@\S+$/i,
                message: 'Invalid email',
              },
            }}
            keyboardType="email-address"
          />
          <Button isLoading={isMutating} mt={30} onPress={handleSubmit(onSubmit)}>
            SIGN UP
          </Button>
        </View>

        <View mt={20} gap={20} display="flex" ai="center" jc="flex-end">
          <View disableSizeMatter bg="neutral30" w={'100%'} h={1} />
          <Text fw="500" color="neutral80" fs="12">
            Or continue with
          </Text>
          <View flexCenterRow gap={14}>
            <BounceTap onPress={() => {}}>
              <View br={100} h={45} flexCenterRow disableSizeMatter w={45} bg="purpleLight">
                <GoogleIcon />
              </View>
            </BounceTap>
            <BounceTap onPress={() => {}}>
              <View br={100} h={45} flexCenterRow disableSizeMatter w={45} bg="purpleLight">
                <AppleIcon />
              </View>
            </BounceTap>
          </View>
          <View mt={30} flexCenterRow>
            <Text fs="12">ALREADY HAVE AN ACCOUNT ?</Text>
            <Text
              onPress={() => {
                router.navigate('Login');
              }}
              fs="12"
              style={{color: '#8E97FD'}}>
              {' '}
              LOG IN
            </Text>
          </View>
        </View>
      </View>
    </AnimatedAuthPipeAnimationWrapper>
  );
};
