// import { Router } from "express";

// export function createTestRouter() {
//   const router = Router();

//   return router;
// }

// export class StudentController {
//   @checkPermission(["listStudents:v"])
//   //student/vi/list
//   static async list(req: Request, res: Response, next: NextFunction) {
//     try {
//     } catch (error) {
//       next(error);
//     }
//   }
//   @checkPermission(["student:list"])
//   static async EditSomething() {}
//   static async EditSomethingElse() {}
// }
// action:group1
// action:group1:id1, action:group1:id2
// checkMedicalRecords:group
// checkPersonalDetails:gro
//[action:group1,action:group1:$param]
// listClass:vi
//
