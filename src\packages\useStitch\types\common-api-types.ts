// packages/useStitch/types/common-types.ts
export interface Pagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

export interface ApiResponse<T> {
  data: T;
  pagination?: Pagination;
  statusCode?: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export type ApiType = 'query' | 'mutation' | 'paginated';

export interface BaseApiConfig {
  path: string | ((params: any) => string);
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  protected: boolean;
  baseCacheKey: string;
  staleTime: number;
  type: ApiType;
  statusCode?: number;
}

export interface QueryApiConfig<T> extends BaseApiConfig {
  type: 'query';
  responseType: ApiResponse<T>;
  defaultData?: ApiResponse<T>;
}

export interface MutationApiConfig<T, B> extends BaseApiConfig {
  type: 'mutation';
  responseType: ApiResponse<T>;
  mutationBody?: B | FormData;
}

export interface PaginatedApiConfig<T> extends BaseApiConfig {
  type: 'paginated';
  responseType: PaginatedResponse<T>;
  isPaginated: true;
  pageSize: number;
  queryParamBuilder?: (params: any) => Record<string, string>;
}
