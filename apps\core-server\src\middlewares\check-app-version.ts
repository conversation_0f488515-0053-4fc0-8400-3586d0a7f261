import type { Request, Response, NextFunction } from "express";
import semver from "semver";
export const checkAppUpdate = async (req: Request, res: Response) => {
  const { currentVersion, buildNumber, platform, env } = req.query;

  // Validate the required query parameters
  if (!currentVersion || !buildNumber || !platform) {
    res.status(400).json({
      success: false,
      message:
        "Missing required parameters (currentVersion, buildNumber, env , or platform)",
    });
    return;
  }

  // Ensure the platform is valid
  if (platform !== "ios" && platform !== "android") {
    res.status(400).json({
      success: false,
      message: "Invalid platform. Please specify either 'ios' or 'android'.",
    });
    return;
  }

  // Convert buildNumber to a number for comparison
  const buildNumberInt = parseInt(buildNumber as string, 10); // Safely cast to string before parsing
  if (isNaN(buildNumberInt)) {
    res.status(400).json({
      success: false,
      message: "Invalid buildNumber. It should be a valid number.",
    });
    return;
  }

  // Parse currentVersion and handle potential invalid version string
  const latestVersion = "1.0.7";
  const latestVersionSemver = semver.parse(latestVersion);
  const currentVersionSemver = semver.parse(currentVersion as string); // Cast currentVersion as string

  // Check if current version is valid
  if (!currentVersionSemver) {
    res.status(400).json({
      success: false,
      message: "Invalid currentVersion. Please provide a valid version string.",
    });
    return;
  }

  // Ensure that semver.parse doesn't return null
  if (!latestVersionSemver) {
    res.status(400).json({
      success: false,
      message: "Invalid latestVersion. Please provide a valid version string.",
    });
    return;
  }

  // Determine if a force update is needed
  const isIosForceUpdate = semver.lt(currentVersionSemver, latestVersionSemver);
  const isAndroidForceUpdate = semver.lt(
    currentVersionSemver,
    latestVersionSemver,
  );

  const shouldForceUpdate =
    platform == "ios"
      ? isIosForceUpdate
      : platform == "android"
        ? isAndroidForceUpdate
        : false;

  res.status(200).json({
    success: true,
    data: {
      isForceUpdate: shouldForceUpdate,
      latestVersion: latestVersion,
      isNonPriorityUpdateAvailable: false,
    },
  });

  return;
};
