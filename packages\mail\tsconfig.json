{"compilerOptions": {"jsx": "react-jsx", "declaration": true, "declarationMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "incremental": false, "isolatedModules": true, "lib": ["es2022", "DOM", "DOM.Iterable"], "module": "NodeNext", "moduleDetection": "force", "moduleResolution": "NodeNext", "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "skipLibCheck": true, "target": "ES2022", "outDir": "./dist", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["**/*.ts", "**/*.tsx", "src/**/*.ts", "src/**/*.d.ts"]}