import { Command } from "@/lib/command-module.js";
import { suggestUniqueUsername } from "@/routes/user/lib.js";
import db from "@repo/db";
import { notificationsTable } from "@repo/db/schema";

import { logger } from "@repo/lib";
import dayjs from "dayjs";
import { sql } from "drizzle-orm";

export const test = new Command({
  name: "test",
  description: "Test Something",
  fn: async () => {
    logger.info("Test Started");

    logger.info("Test Done");
  },
});

function getAutoCancelDate(dates: Date[]): Date | undefined {
  const today = dayjs();
  for (const date of dates) {
    if (dayjs(date).diff(today, "day") < 3) {
      return date;
    }
  }
  return undefined;
}
