version: "3.8"

services:
  core-server:
    build:
      context: .
      dockerfile: apps/core-server/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    networks:
      - app-network
    env_file:
      - .env
    volumes:
      - ./logs/core-server:/app/logs

  # chat-server:
  #   build:
  #     context: .
  #     dockerfile: apps/chat-server/Dockerfile
  #   ports:
  #     - "4000:4000"
  #   environment:
  #     - NODE_ENV=production
  #   networks:
  #     - app-network
  #   env_file:
  #     - .env
  #   volumes:
  #     - ./logs/chat-server:/app/logs

  # cron-server:
  #   build:
  #     context: .
  #     dockerfile: apps/cron-server/Dockerfile
  #   ports:
  #     - "3001:3000"
  #   environment:
  #     - NODE_ENV=production
  #   networks:
  #     - app-network
  #   env_file:
  #     - .env
  #   volumes:
  #     - ./logs/cron-server:/app/logs

networks:
  app-network:
    driver: bridge
