{"name": "@repo/lib", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "tsc --project tsconfig.json && tsc-alias -p tsconfig.json", "lint": "eslint . --max-warnings 0"}, "exports": {".": {"production": {"import": "./dist/index.js", "require": "./dist/index.js", "default": "./dist/index.js"}, "default": "./src/index.ts"}, "./env": {"production": {"import": "./dist/env.js", "require": "./dist/index.js", "default": "./dist/index.js"}, "default": "./src/env.ts"}}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/eslint": "^8.56.5", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.16.5", "eslint": "^8.57.0", "tsc-alias": "^1.8.10", "typescript": "5.5.4"}, "dependencies": {"@aws-sdk/client-s3": "^3.693.0", "@repo/db": "workspace:^", "@types/express-fileupload": "^1.5.1", "country-state-city": "^3.2.1", "dayjs": "^1.11.13", "dayjs-plugin-utc": "^0.1.2", "drizzle-orm": "^0.36.0", "express-fileupload": "^1.5.1", "firebase-admin": "^13.0.2", "jsonwebtoken": "^9.0.2", "winston": "^3.16.0", "winston-daily-rotate-file": "^5.0.0"}}