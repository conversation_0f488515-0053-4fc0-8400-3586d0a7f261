import {useTheme} from '@context/index';
import {LinearGradient} from 'react-native-linear-gradient';
import React, {useEffect} from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';
import Animated, {Easing, interpolate, useAnimatedStyle, useSharedValue, withRepeat, withTiming} from 'react-native-reanimated';

const {width} = Dimensions.get('window');

export const SkeletonPostCard: React.FC = () => {
  const {colors} = useTheme();
  // Single shared value for animation progress
  const blipProgress = useSharedValue(0);

  // Dimensions
  const avatarWidth = 40;
  const gap = 16;
  const contentWidth = width - 32 - avatarWidth - gap; // Screen width - padding (16 * 2) - avatar - gap

  // Animation setup
  useEffect(() => {
    blipProgress.value = withRepeat(
      withTiming(1, {
        duration: 1000, // Snappy pulse
        easing: Easing.inOut(Easing.sin), // Smooth, natural pulse
      }),
      -1, // Infinite repeat
      true, // Reverse for natural fade in/out
    );
  }, [blipProgress]);

  // Animated style for avatar gradient (blipping)
  const avatarBlipStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      blipProgress.value,
      [0, 0.5, 1], // Pulse peaks at 0.5
      [0.3, 0.8, 0.3], // Fade from low to high and back
    );
    return {
      opacity,
    };
  });

  // Animated style for content gradient (blipping)
  const contentBlipStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      blipProgress.value,
      [0.2, 0.7, 1], // Slightly delayed peak for content
      [0.3, 0.8, 0.3], // Same opacity range
    );
    return {
      opacity,
    };
  });

  return (
    <View style={[styles.postCard, {backgroundColor: colors.transparent}]}>
      {/* Avatar Skeleton */}
      <Animated.View style={[styles.avatar, {overflow: 'hidden', backgroundColor: colors.neutral20}]}>
        <Animated.View style={[styles.gradientContainer, avatarBlipStyle]}>
          <LinearGradient
            colors={[colors.neutral10, colors.neutral00]}
            start={{x: 0, y: 0}} // Left
            end={{x: 1, y: 0}} // Right
            style={[styles.gradient, {width: avatarWidth}]} // Match avatar width
          />
        </Animated.View>
      </Animated.View>

      {/* Content Rod Skeleton */}
      <Animated.View style={[styles.content, {overflow: 'hidden', backgroundColor: colors.neutral20}]}>
        <Animated.View style={[styles.gradientContainer, contentBlipStyle]}>
          <LinearGradient
            colors={[colors.neutral10, colors.neutral00]}
            // colors={[colors.neutral00, colors.neutral10, colors.neutral00]}
            start={{x: 0, y: 0}} // Left
            end={{x: 1, y: 0}} // Right
            style={[styles.gradient, {width: contentWidth}]} // Match content width
          />
        </Animated.View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  postCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3, // Subtle shadow for depth
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  content: {
    flex: 1,
    height: 20,
    marginLeft: 16,
    borderRadius: 50, // Subtle for modern look
  },
  gradientContainer: {
    position: 'absolute',
    height: '100%',
  },
  gradient: {
    height: '100%',
  },
});
