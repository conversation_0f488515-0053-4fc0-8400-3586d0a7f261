import { GetMoodCalenderResponse, GetMoodWeekResponse, LogMoodResponse } from '../types';
import { ApiResponse, MutationApiConfig } from '../types/common-api-types';

export namespace MoodApis {
    export const logMood = 'logMood' as const;
    export const getMoodWeek = 'getMoodWeek' as const;
    export const getMoodCalender = 'getMoodCalender' as const;
}

export const moodApiConfig = {
    logMood: {
        path: '/mood/log',
        method: 'POST',
        protected: true,
        mutationBody: undefined as unknown as any,
        responseType: undefined as unknown as ApiResponse<LogMoodResponse>,
        baseCacheKey: 'log-mood',
        staleTime: 0,
        type: 'mutation' as const,
    } satisfies MutationApiConfig<LogMoodResponse, any>,
    getMoodWeek: {
        path: '/mood/week',
        method: 'GET',
        protected: true,
        responseType: undefined as unknown as ApiResponse<GetMoodWeekResponse>,
        baseCacheKey: 'get-mood-week',
        staleTime: 0,
        type: 'mutation' as const,
    } satisfies MutationApiConfig<GetMoodWeekResponse, any>,
    getMoodCalender: {
        path: '/mood/calendar',
        method: 'GET',
        protected: true,
        responseType: undefined as unknown as ApiResponse<GetMoodCalenderResponse>,
        baseCacheKey: 'get-mood-calendar',
        staleTime: 0,
        type: 'mutation' as const,
    } satisfies MutationApiConfig<GetMoodCalenderResponse, any>,
};
