#!/bin/bash

# <PERSON><PERSON>t to build and install local packages in the React Native app

echo "Building local packages..."

# Navigate to the local package directory
cd packages/react-native-live-markdown-local || { echo "Package directory not found!"; exit 1; }

# Install dependencies for the package
npm install || { echo "Failed to install package dependencies!"; exit 1; }

# Build the package
npm run build || { echo "Failed to build package!"; exit 1; }

echo "Package built successfully!"

# Navigate back to the app root
cd ../..

# Install app dependencies to link the local package
npm install || { echo "Failed to install app dependencies!"; exit 1; }

# Update iOS pods for native code
cd ios
pod install || { echo "Failed to install iOS pods!"; exit 1; }
cd ..

echo "Local package installed successfully!"

# Optional: Rebuild the app (uncomment if needed)
# npx react-native run-ios
# npx react-native run-android