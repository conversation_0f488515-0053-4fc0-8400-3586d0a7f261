import { AuthMiddlewares } from "@/middlewares/auth-middleware.js";
import { Router } from "express";
import UserController from "./controller.js";

export function createUserRouter() {
  const router = Router();
  router.post(
    "/complete-signup",
    AuthMiddlewares.validateAccessToken,
    UserController.addUserDetails,
  );
  router.get("/username-availability", UserController.usernameAvailability);
  router.get("/get-username", UserController.getSuggestedUsername);
  router.post("/update", UserController.updateDetails);
  router.get("/me", AuthMiddlewares.validateAccessToken, UserController.me);
  router.post("/visibility", UserController.toggleVisibility);
  router.post("/toggle-notifications", UserController.toggleNotifications);
  router.get("/get-avatars", UserController.getAvatarList);
  return router;
}
