import {ApiResponse, QueryApiConfig} from '../types';
import {GetOnboardingQuestionsResponse} from '../types/questionaire-api-types';

export namespace QuestionaireApis {
  export const getOnboardingQuestions = 'getOnboardingQuestions' as const;
}

export const questionaireApiConfig = {
  getOnboardingQuestions: {
    path: '/sandbox/get-questions',
    method: 'GET',
    protected: true,
    responseType: undefined as unknown as ApiResponse<GetOnboardingQuestionsResponse>,
    baseCacheKey: 'get-questions',
    staleTime: 0,
    type: 'query' as const,
    defaultData: {data: []},
  } satisfies QueryApiConfig<GetOnboardingQuestionsResponse>,
};
