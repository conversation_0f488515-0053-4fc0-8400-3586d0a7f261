import AppLogo from '@assets/svgs/app-logo.svg';
import {Button, Text, View} from '@components/native';
import {StaggeredMotiFadeInAnim} from '@components/shared/animated';
import {ENV} from '@config/env';
import {router} from '@navigation/refs/navigation-ref';
import React, {useEffect, useRef, useState} from 'react';
import {Dimensions, FlatList, Image, ImageSourcePropType, NativeScrollEvent, NativeSyntheticEvent, ViewStyle} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import IoniIcons from 'react-native-vector-icons/Ionicons';
const {width, height} = Dimensions.get('screen');

console.log(ENV);

const carouselItems = [
  {
    image: require('../../../assets/images/intro-1.png'),
    text1: 'Feeling',
    text2: 'Heartbroken ?',
  },
  {
    image: require('../../../assets/images/intro-2.png'),
    text1: 'Facing Mental',
    text2: 'Breakdown?',
  },
  {
    image: require('../../../assets/images/intro-3.png'),
    text1: 'Loss of a furry',
    text2: 'friend?',
  },
] as const;

export const IntroScreen = () => {
  // const {Sheet, closeSheet, openSheet} = useBottomSheet();
  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef<FlatList<any>>(null);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(offsetX / width);
    setActiveIndex(index);
  };

  const renderDot = (index: number) => {
    const isActive = activeIndex === index;
    const style: ViewStyle = {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#FFFFFF',
      opacity: isActive ? 1 : 0.3,
    };

    return <View key={index} style={style} />;
  };

  useEffect(() => {
    // const interval = setInterval(() => {
    //   const nextIndex = (activeIndex + 1) % carouselItems.length;
    //   flatListRef.current?.scrollToIndex({index: nextIndex, animated: true});
    //   setActiveIndex(nextIndex);
    // }, 5000);
    // return () => clearInterval(interval);
  }, [activeIndex]);

  return (
    <View flex={1} display="flex" fd="column">
      <FlatList
        ref={flatListRef}
        initialNumToRender={3}
        data={carouselItems}
        renderItem={({index, item}) => {
          return <CarouselItem key={index} item={item} />;
        }}
        keyExtractor={(_, index) => index.toString()}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        scrollToOverflowEnabled={false}
        bounces={false}
      />
      <View pos="absolute" bottom={60} w={width} disableSizeMatter flexCenterColumn gap={20}>
        <View display="flex" fd="row" gap={8}>
          {carouselItems.map((_, index) => renderDot(index))}
        </View>
        <StaggeredMotiFadeInAnim>
          <Button bg="white" color="black" px={26} h={48} rightIcon={<IoniIcons size={20} name="arrow-forward" />} isFullWidth={false} onPress={() => router.navigate('Login')}>
            Join us Now
          </Button>
        </StaggeredMotiFadeInAnim>
      </View>
    </View>
  );
};

type CarouselItemProps = {
  item: {
    image: ImageSourcePropType;
    text1: string;
    text2: string;
  };
};
const CarouselItem: React.FC<CarouselItemProps> = ({item}) => {
  const {top} = useSafeAreaInsets();
  return (
    <View fd="column" flex={1} disableSizeMatter style={{width, height: height}}>
      <View flexCenterColumn gap={20} disableSizeMatter w={width} pos="absolute" z={1} top={20 + top}>
        <StaggeredMotiFadeInAnim>
          <AppLogo />
        </StaggeredMotiFadeInAnim>
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            width: '100%',
          }}>
          <StaggeredMotiFadeInAnim>
            <Text fw="500" fs="26" ff="PlayfairDisplay-Medium" color="text">
              {item.text1}
            </Text>
            <Text fw="500" ff="PlayfairDisplay-Medium" fs="26" color="text">
              {item.text2}
            </Text>
          </StaggeredMotiFadeInAnim>
        </View>
      </View>
      <View style={{flex: 1, width, height: height - 150}}>
        <Image
          source={item.image}
          style={{
            width,
            height: '100%',
          }}
          resizeMode="cover"
        />
      </View>
    </View>
  );
};
