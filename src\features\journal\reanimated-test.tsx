// import React, {useRef, useState} from 'react';
// import {Dimensions, StyleSheet, FlatList, ScrollView} from 'react-native';
// import {View, Text, TextInput} from '@components/native';
// import {TabView, TabBar, SceneMap} from 'react-native-tab-view';
// import {useTheme} from '@context/index';
// import {useSafeAreaInsets} from 'react-native-safe-area-context';
// import {FlashList} from '@shopify/flash-list';
// import {useForm} from 'react-hook-form';
// import Animated, {useSharedValue, useAnimatedStyle, useAnimatedScrollHandler, interpolate, Extrapolation, useAnimatedReaction, runOnJS} from 'react-native-reanimated';

// const {width} = Dimensions.get('screen');

// interface MockJournalItem {
//   id: string;
//   type: string;
//   title: string;
//   totalEntries: number;
//   bgImage?: string;
// }

// const MOCK_JOURNAL_TYPES: MockJournalItem[] = [
//   {id: '0', type: 'all', title: 'All', totalEntries: 2},
//   {id: '1', type: 'gratitude', title: 'Gratitude', totalEntries: 20},
//   {id: '2', type: 'lost', title: 'Lost', totalEntries: 20},
//   {id: '3', type: 'anger', title: 'Anger', totalEntries: 18},
//   {id: '4', type: 'love', title: 'Love', totalEntries: 10},
//   {id: '5', type: 'anxiety', title: 'Anxiety', totalEntries: 8},
//   {id: '6', type: 'affection', title: 'Affection', totalEntries: 8},
//   {id: '7', type: 'happiness', title: 'Happiness', totalEntries: 5},
//   {id: '8', type: 'sadness', title: 'Sadness', totalEntries: 9},
//   {id: '9', type: 'hope', title: 'Hope', totalEntries: 3},
//   {id: '10', type: 'fear', title: 'Fear', totalEntries: 6},
//   {id: '11', type: 'stress', title: 'Stress', totalEntries: 11},
//   {id: '12', type: 'peace', title: 'Peace', totalEntries: 7},
//   {id: '13', type: 'confusion', title: 'Confusion', totalEntries: 4},
//   {id: '14', type: 'joy', title: 'Joy', totalEntries: 15},
//   {id: '15', type: 'motivation', title: 'Motivation', totalEntries: 13},
//   {id: '16', type: 'reflection', title: 'Reflection', totalEntries: 12},
//   {id: '17', type: 'calmness', title: 'Calmness', totalEntries: 6},
//   {id: '18', type: 'excitement', title: 'Excitement', totalEntries: 14},
//   {id: '19', type: 'sorrow', title: 'Sorrow', totalEntries: 7},
//   {id: '20', type: 'inspiration', title: 'Inspiration', totalEntries: 10},
// ];

// interface FormValues {
//   query: string;
// }

// interface TabRoute {
//   key: string;
//   title: string;
// }

// // Move renderItem to a separate component to avoid hook issues
// const CarouselItem: React.FC<{
//   item: MockJournalItem;
//   index: number;
//   scrollX: Animated.SharedValue<number>;
//   cardWidth: number;
//   spacing: number;
// }> = ({item, index, scrollX, cardWidth, spacing}) => {
//   const animatedStyle = useAnimatedStyle(() => {
//     const inputRange = [(index - 1) * (cardWidth + spacing), index * (cardWidth + spacing), (index + 1) * (cardWidth + spacing)];
//     return {
//       transform: [
//         {
//           scale: interpolate(scrollX.value, inputRange, [0.8, 1, 0.8], Extrapolation.CLAMP),
//         },
//       ],
//       width: cardWidth,
//       marginHorizontal: spacing / 2,
//     };
//   });

//   return (
//     <Animated.View style={animatedStyle}>
//       <View
//         style={{
//           height: cardWidth,
//           backgroundColor: 'black',
//           borderRadius: 16,
//           justifyContent: 'center',
//           alignItems: 'center',
//         }}>
//         <Text style={{color: 'white', fontSize: 16}}>{item.title}</Text>
//       </View>
//     </Animated.View>
//   );
// };

// // Create a scene for each tab
// const renderScene = SceneMap(
//   MOCK_JOURNAL_TYPES.reduce(
//     (scenes, item, index) => ({
//       ...scenes,
//       [item.id]: () => (
//         <FlashList
//           ItemSeparatorComponent={() => <View style={{height: 10}} />}
//           data={Array.from({length: 200}).fill(MOCK_JOURNAL_TYPES[index])}
//           contentContainerStyle={{padding: 10}}
//           renderItem={({item, index}) => {
//             const obj = item as MockJournalItem;
//             return (
//               <View
//                 style={{
//                   height: 100,
//                   borderRadius: 10,
//                   justifyContent: 'center',
//                   alignItems: 'center',
//                   backgroundColor: '#ff4d4d',
//                 }}>
//                 <Text style={{color: 'white'}}>
//                   {obj.title} {index}
//                 </Text>
//               </View>
//             );
//           }}
//           estimatedItemSize={110}
//         />
//       ),
//     }),
//     {} as Record<string, React.FC>,
//   ),
// );

// const CustomTabBar = ({...props}: any) => (
//   <View style={{backgroundColor: 'orange'}}>
//     <TabBar
//       {...props}
//       indicatorStyle={styles.indicator}
//       style={styles.tabBar}
//       labelStyle={styles.tabLabel}
//       scrollEnabled={true}
//       tabStyle={styles.tab}
//     />
//   </View>
// );

// const CustomTopTabBar: React.FC = () => {
//   const {control, handleSubmit} = useForm<FormValues>({
//     defaultValues: {query: ''},
//   });
//   const {colors} = useTheme();
//   const layout = Dimensions.get('window');
//   const [index, setIndex] = useState(0);
//   const [routes] = useState<TabRoute[]>(MOCK_JOURNAL_TYPES.map(item => ({key: item.id, title: item.title})));
//   const flatListRef = useRef<FlatList>(null);
//   const scrollX = useSharedValue(0);
//   const isTransitionOngoing = useSharedValue(false);

//   const CARD_WIDTH = layout.width * 0.45;
//   const SPACING = 20;
//   const SIDE_SPACER = (layout.width - CARD_WIDTH) / 2;

//   // Sync carousel scroll with TabView index
//   useAnimatedReaction(
//     () => ({scrollX: scrollX.value, isTransitionOngoing: isTransitionOngoing.value}),
//     ({scrollX, isTransitionOngoing}) => {
//       if (isTransitionOngoing) return;
//       const newIndex = Math.round(scrollX / (CARD_WIDTH + SPACING));
//       if (newIndex !== index && newIndex >= 0 && newIndex < MOCK_JOURNAL_TYPES.length) {
//         runOnJS(setIndex)(newIndex);
//       }
//     },
//     [index, CARD_WIDTH, SPACING],
//   );

//   // Handle TabView index change to scroll carousel
//   const handleIndexChange = (newIndex: number) => {
//     if (newIndex === index) return;
//     isTransitionOngoing.value = true;
//     setIndex(newIndex);
//     if (flatListRef.current) {
//       flatListRef.current.scrollToIndex({
//         index: newIndex,
//         animated: Math.abs(newIndex - index) <= 1, // smooth for nearby tabs, instant for far jumps
//       });
//     }
//     // Dynamic timeout based on index jump distance
//     const jumpDistance = Math.abs(newIndex - index);
//     const timeoutDuration = Math.max(300, jumpDistance * 50); // 50ms per index
//     setTimeout(() => {
//       isTransitionOngoing.value = false;
//     }, timeoutDuration);
//   };

//   const scrollHandler = useAnimatedScrollHandler({
//     onScroll: event => {
//       scrollX.value = event.contentOffset.x;
//     },
//     onBeginDrag: () => {
//       isTransitionOngoing.value = true;
//     },
//     onMomentumEnd: () => {
//       isTransitionOngoing.value = false;
//     },
//   });

//   return (
//     <View style={styles.container}>
//       <View
//         bg="orange"
//         pb={25}>
//         <View style={{padding: 20}}>
//           <View
//             bc="neutral20"
//             bw={2}
//             br={16}>
//             <TextInput
//               control={control}
//               gapBottom={0}
//               name="query"
//               label="Search anything"
//               labelType="background"
//             />
//           </View>
//         </View>
//         <Animated.FlatList
//           ref={flatListRef}
//           pagingEnabled
//           data={MOCK_JOURNAL_TYPES}
//           renderItem={({item, index}) => (
//             <CarouselItem
//               item={item}
//               index={index}
//               scrollX={scrollX}
//               cardWidth={CARD_WIDTH}
//               spacing={SPACING}
//             />
//           )}
//           keyExtractor={item => item.id}
//           horizontal
//           showsHorizontalScrollIndicator={false}
//           snapToInterval={CARD_WIDTH + SPACING}
//           decelerationRate="fast"
//           bounces={false}
//           contentContainerStyle={{paddingHorizontal: SIDE_SPACER}}
//           onScroll={scrollHandler}
//           scrollEventThrottle={16}
//           getItemLayout={(data, index) => ({
//             length: CARD_WIDTH + SPACING,
//             offset: (CARD_WIDTH + SPACING) * index,
//             index,
//           })}
//         />
//       </View>
//       <TabView
//         navigationState={{index, routes}}
//         renderScene={renderScene}
//         onIndexChange={handleIndexChange}
//         initialLayout={{width: layout.width}}
//         renderTabBar={CustomTabBar}
//       />
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: 'white',
//   },
//   header: {
//     backgroundColor: '#043d79',
//     width: width,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   headerTitle: {
//     fontWeight: '700',
//     fontSize: 18,
//     color: 'white',
//   },
//   rangoli: {
//     height: 100,
//     width: 100,
//     tintColor: '#fff',
//     position: 'absolute',
//     right: -10,
//     opacity: 0.1,
//   },
//   tabBar: {
//     backgroundColor: '#043d79',
//   },
//   tab: {
//     width: 'auto',
//     paddingHorizontal: 10,
//   },
//   tabLabel: {
//     color: 'white',
//     fontSize: 16,
//     fontWeight: '500',
//   },
//   indicator: {
//     backgroundColor: 'white',
//     height: 2,
//   },
//   tabContent: {
//     flex: 1,
//     alignItems: 'center',
//     justifyContent: 'center',
//     backgroundColor: '#f5f5f5',
//   },
//   tabText: {
//     fontSize: 18,
//     color: '#333',
//   },
//   card: {
//     width: width,
//     height: 180,
//     borderRadius: 20,
//     backgroundColor: 'black',
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   cardText: {
//     color: 'white',
//     fontSize: 16,
//     fontWeight: '500',
//   },
// });

// export default CustomTopTabBar;
