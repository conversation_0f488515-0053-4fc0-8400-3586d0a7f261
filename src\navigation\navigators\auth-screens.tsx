import { Text } from '@components/native';
import { ChatScreen } from '@features/chat';
import { PostAuthAddPersonalDetails } from '@features/user';
import DrawerScreens from '@navigation/navigators/drawer-screens';
import { createStackNavigator } from '@react-navigation/stack';
import SCREENS from '@src/SCREENS';
import React, { lazy, Suspense } from 'react';
import { publicScreenComponents } from './public-screens-components';

const authComponents = {
  [SCREENS.ONBOARDING]: lazy(() =>
    import('@features/user/Onboarding').then(module => ({
      default: module.Onboarding,
    })),
  ),
  [SCREENS.BOOKMARKS]: lazy(() =>
    import('@features/drawer/Bookmarks').then(module => ({
      default: module.BookmarksScreen,
    })),
  ),
  [SCREENS.FAQS]: lazy(() =>
    import('@features/drawer/Faqs').then(module => ({
      default: module.FaqsScreen,
    })),
  ),
  [SCREENS.BLOCKED_CONTACTS]: lazy(() =>
    import('@features/drawer/BlockedUsers').then(module => ({
      default: module.BlockedUsersScreen,
    })),
  ),
  [SCREENS.SETTINGS]: lazy(() =>
    import('@features/drawer/Settings').then(module => ({
      default: module.SettingsScreen,
    })),
  ),
  [SCREENS.PRIVACY_POLICY]: lazy(() =>
    import('@features/drawer/PrivacyPolicy').then(module => ({
      default: module.PrivacyPolicyScreen,
    })),
  ),
  [SCREENS.TERMS_OF_SERVICE]: lazy(() =>
    import('@features/drawer/TermsOfService').then(module => ({
      default: module.TermsOfServiceScreen,
    })),
  ),
  [SCREENS.ADD_JOURNAL]: lazy(() =>
    import('@features/journal/AddJournal').then(module => ({
      default: module.AddJournal,
    })),
  ),
  [SCREENS.CHOOSE_JOURNAL_TYPE]: lazy(() =>
    import('@features/journal/ChooseJournalType').then(module => ({
      default: module.ChooseJournalType,
    })),
  ),
  [SCREENS.CREATE_JOURNAL_TYPE]: lazy(() =>
    import('@features/journal/CreateJournalType').then(module => ({
      default: module.CreateJournalType,
    })),
  ),
  [SCREENS.JOURNAL_SEARCH]: lazy(() =>
    import('@features/journal/SearchJournal').then(module => ({
      default: module.SearchJournal,
    })),
  ),
  [SCREENS.JOURNAL_DETAILS]: lazy(() =>
    import('@features/journal/JournalDetails').then(module => ({
      default: module.JournalDetails,
    })),
  ),
  [SCREENS.EDIT_PROFILE]: lazy(() =>
    import('@features/profile/EditProfile').then(module => ({
      default: module.EditProfile,
    })),
  ),
  [SCREENS.SELECT_AVATAR]: lazy(() =>
    import('@features/profile/SelectAvatar').then(module => ({
      default: module.SelectAvatarScreen,
    })),
  ),
  [SCREENS.EDIT_POST]: lazy(() =>
    import('@features/feed/EditPost').then(module => ({
      default: module.EditPost,
    })),
  ),
  [SCREENS.MOOD_ARTICLE]: lazy(() =>
    import('@features/mood-tracker/Article').then(module => ({
      default: module.MoodArticle,
    })),
  ),
  [SCREENS.MOOD_CALENDAR]: lazy(() =>
    import('@features/mood-tracker/Calendar').then(module => ({
      default: module.MoodCalendar,
    })),
  ),
  [SCREENS.CREATE_NEW_POST]: lazy(() =>
    import('@features/feed/CreatePost').then(module => ({
      default: module.CreatePost,
    })),
  ),
  [SCREENS.CREATE_NEW_POLL]: lazy(() =>
    import('@features/feed/CreatePoll').then(module => ({
      default: module.CreatePoll,
    })),
  ),
  ...publicScreenComponents, // do not remove this
};

const Stack = createStackNavigator();
const Fallback = () => <Text>Loading...</Text>;
const AuthenticatedScreens = ({ isSignupCompleted = false }: { isSignupCompleted: boolean }) => {
  return (
    <Suspense fallback={<Fallback />}>
      <Stack.Navigator initialRouteName={isSignupCompleted ? 'drawer' : SCREENS.GET_STARTED} screenOptions={{ headerShown: false }}>
        <Stack.Screen name="drawer" component={DrawerScreens} />
        <Stack.Screen name={SCREENS.CHAT} component={ChatScreen} />
        <Stack.Screen name={SCREENS.GET_STARTED} component={PostAuthAddPersonalDetails} />
        {/* Dynamically render lazy-loaded screens */}
        {Object.entries(authComponents).map(([name, component]) => (
          <Stack.Screen key={name} name={name} component={component} />
        ))}
      </Stack.Navigator>
    </Suspense>
  );
};

export default AuthenticatedScreens;
