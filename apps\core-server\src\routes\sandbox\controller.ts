import { OnboardRepo } from "@repo/repo";
import type { Request, Response, NextFunction } from "express";
export class SandboxController {
  static async getOnboardQuestions(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const questions = await OnboardRepo.getQuestions();
      res.json({ data: questions, success: true });
    } catch (error) {
      next(error);
    }
  }
}
