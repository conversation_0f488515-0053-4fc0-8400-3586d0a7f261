import type { Request, Response, NextFunction } from "express";
import { City, Country, State } from "country-state-city";
import db, { objectBuilder, sqlConcat } from "@repo/db";
import { faqTable, users } from "@repo/db/schema";
import { and, asc, count, desc, eq, max, sql } from "drizzle-orm";
import { pathGenerator, S3Bucket } from "@repo/lib";

export class PublicController {
  static async getCountries(req: Request, res: Response, next: NextFunction) {
    try {
      const countries = Country.getAllCountries().map((country) => ({
        ...country,

        timezones: undefined,
        latitude: undefined,
        longitude: undefined,
      }));
      res.json({
        message: "List of all countries",
        data: countries,
      });
    } catch (error) {
      next(error);
    }
  }

  static async getStatesByCountry(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const states = State.getStatesOfCountry(req.params.id).map((state) => ({
        ...state,
        latitude: undefined,
        longitude: undefined,
      }));
      res.json({
        message: "List of all states",
        data: states,
      });
    } catch (error) {
      next(error);
    }
  }

  static async getCitiesByStateAndCountry(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      if (!req.params.countryId || !req.params.stateId) {
        res
          .status(422)
          .json({ message: "Country ID and state ID both are required" });
        return;
      }
      let cities: { name: string; stateCode: string }[] = City.getCitiesOfState(
        req.params.countryId,
        req.params.stateId,
      ).map((c) => ({
        ...c,

        latitude: undefined,
        longitude: undefined,
        countryCode: undefined,
      }));
      if (cities.length == 0) {
        const city = State.getStateByCodeAndCountry(
          req.params.stateId,
          req.params.countryId,
        );
        if (!city) {
          res.json({ message: "No such state", data: [] });
          return;
        }
        cities = [
          {
            name: city?.name,
            stateCode: city?.isoCode,
          },
        ];
      }

      res.json({
        message: "List of all cities",
        data: cities,
      });
    } catch (error) {
      next(error);
    }
  }
  static async getPointsConversationRates(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const values = {
        USD: 1,
        EUR: 1.03,
        JPY: 0.0063,
        GBP: 1.23,
        AUD: 0.62,
        CAD: 0.69,
        CHF: 1.1,
        CNY: 0.14,
        INR: 0.012,
        RUB: 0.0098,
      };
      res.json({ data: values });
    } catch (error) {
      next(error);
    }
  }

  static async getFaq(req: Request, res: Response, next: NextFunction) {
    try {
      const faq = await db
        .select({
          id: faqTable.id,
          question: faqTable.question,
          answer: faqTable.answer,
          order: faqTable.order,
        })
        .from(faqTable)
        .where(eq(faqTable.visibility, "public"))
        .orderBy(asc(faqTable.order));
      res.json({ data: faq });
    } catch (error) {
      next(error);
    }
  }
  static async getCustomerSupport(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const [customerSupport] = await db
        .select({ userId: users.id, email: users.email })
        .from(users)
        .where(
          and(
            eq(users.email, "<EMAIL>"),
            eq(users.role, "admin"),
          ),
        );
      if (!customerSupport) {
        throw new Error("Admin needs to be seeded ");
      }
      res.json({
        data: {
          userId: customerSupport?.userId,
          email: customerSupport.email,
          avatar: {
            url: S3Bucket.getAwsUrl(pathGenerator.asset.supportImage),
            preview: S3Bucket.getAwsUrl(pathGenerator.asset.supportImage),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
