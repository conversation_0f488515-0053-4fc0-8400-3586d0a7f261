import {Database, Q} from '@nozbe/watermelondb';
import {SessionUser} from '@packages/useStitch/types';
import {Observable} from 'rxjs';
import {UserModel} from '../model/user-schema';

type setLocalUserExtras = {
  isSignupCompleted: boolean;
  accessToken: string;
  refreshToken: string;
  isAuthenticated: boolean;
};
export class UserDb {
  private database: Database;

  constructor(database: Database) {
    this.database = database;
  }

  private upsertUserInfo(u: UserModel, extras: setLocalUserExtras, userData?: SessionUser) {
    u.username = userData?.username ?? '';
    u.avatar = userData?.avatar ?? '';
    u.location = userData?.location ?? '';
    u.bio = userData?.bio ?? '';
    u.email = userData?.email ?? '';
    u.playlistLink = userData?.playlistLink ?? '';
    u.gender = userData?.gender ?? '';
    u.age = userData?.age ?? '';
    u.totalPostCount = userData?.totalPostCount ?? 0;
    u.totalHealCount = userData?.totalHealCount ?? 0;
    u.totalFollowers = userData?.totalFollowers ?? 0;
    u.totalFollowing = userData?.totalFollowing ?? 0;
    u.idShowCurrentMoodOnProfile = userData?.showCurrentMoodOnProfile ?? false;
    u.isSignupCompleted = extras.isSignupCompleted ?? '';
    u.isAuthenticated = extras.isAuthenticated ?? '';
    u.accessToken = extras.accessToken;
    u.refreshToken = extras.refreshToken;
    u.lastUpdatedAt = Date.now();
    return u;
  }

  async setUser(extras: setLocalUserExtras, userData?: SessionUser) {
    try {
      await this.database.write(async () => {
        const existingUsers = await this.database.get<UserModel>('user_info').query(Q.where('refresh_token', extras.refreshToken)).fetch();

        let user: UserModel;
        if (existingUsers.length > 0) {
          user = existingUsers[0];
          const updatedUser = await user.update(u => this.upsertUserInfo(u, extras, userData));
        } else {
          user = await this.database.get<UserModel>('user_info').create(u => this.upsertUserInfo(u, extras, userData));
        }

        return user;
      });
    } catch (error) {
      console.error('Error setting user:', error);
    }
  }

  async getUser(): Promise<UserModel | null> {
    const users = await this.database.get<UserModel>('user_info').query(Q.where('is_authenticated', true), Q.sortBy('last_updated_at', Q.desc), Q.take(1)).fetch();
    return users.length > 0 ? users[0] : null;
  }

  getReactiveUser(): Observable<UserModel[]> {
    return this.database.get<UserModel>('user_info').query(Q.where('is_authenticated', true), Q.sortBy('last_updated_at', Q.desc), Q.take(1)).observe();
  }

  async logout(): Promise<void> {
    return this.database.write(async () => {
      const users = await this.database.get<UserModel>('user_info').query(Q.where('is_authenticated', true)).fetch();
      for (const user of users) {
        await user.update(u => {
          u.isAuthenticated = false;
          u.accessToken = '';
          u.refreshToken = '';
          u.lastUpdatedAt = Date.now();
        });
      }
    });
  }
  async deleteAllUsers(): Promise<void> {
    try {
      await this.database.write(async () => {
        const allUsers = await this.database.get<UserModel>('user_info').query().fetch();
        for (const user of allUsers) {
          await user.destroyPermanently();
        }
      });
    } catch (error) {
      console.error('Error deleting all users:', error);
      throw error;
    }
  }
}
