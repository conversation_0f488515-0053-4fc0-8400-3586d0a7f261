import { mediaBodyValidator } from "@/lib/validation-module.js";
import db from "@repo/db";
import { journalCategoryTable, journalTable } from "@repo/db/schema";
import { and, count, eq, isNull, or } from "drizzle-orm";
import type { Request, Response, NextFunction } from "express";
import { createJournalCategoryValidator } from "./validator.js";
import { MediaModule } from "@repo/bin-packages";

export class JournalCategoriesController {
  static async getAllCategories(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const rows = await db
        .select({
          id: journalCategoryTable.id,
          title: journalCategoryTable.title,
          type: journalCategoryTable.type,
          bgImage: journalCategoryTable.bgImage,
          totalCount: count(journalCategoryTable.id),
        })
        .from(journalCategoryTable)
        .leftJoin(
          journalTable,
          and(
            eq(journalTable.categoryId, journalCategoryTable.id),
            isNull(journalTable.deletedAt),
            eq(journalTable.userId, req.user.id),
          ),
        )
        .where(
          and(
            or(
              isNull(journalCategoryTable.userId),
              eq(journalCategoryTable.userId, req.user.id),
            ),
            isNull(journalTable.deletedAt),
          ),
        )
        .groupBy(journalCategoryTable.id, journalTable.categoryId);
      const totalJournals = rows.reduce((acc, row) => {
        return acc + row.totalCount;
      }, 0);
      res.json({
        data: {
          data: rows.map((row) => ({
            ...row,
            totalCount: row.type == "all" ? totalJournals : row.totalCount,
          })),
        },
      });
    } catch (error) {
      next(error);
    }
  }
  @mediaBodyValidator(createJournalCategoryValidator)
  static async addCategory(req: Request, res: Response, next: NextFunction) {
    try {
      // throw new Error("Just testing");
      const [bgImage] =
        req.files?.bgImage instanceof Array
          ? req.files.bgImage
          : [req.files?.bgImage];
      if (!bgImage) {
        res.status(400).json({ message: "Please upload a photo" });
        return;
      }

      const file = await MediaModule.uploadJournalCover(req.user.id, bgImage);
      if (!file) {
        res.status(500).json({ message: "Something went wrong" });
        return;
      }

      const [category] = await db
        .insert(journalCategoryTable)
        .values({
          userId: req.user.id,
          title: req.body.title,
          type: req.body.type,
          bgImage: file.url,
        })
        .returning();
      res.json({
        data: { data: category },
        message: "Category created successfully",
      });
    } catch (error) {
      next(error);
    }
  }
}
