{"name": "@repo/bin-packages", "version": "0.0.0", "private": true, "type": "module", "exports": {".": {"production": {"import": "./dist/index.js"}, "default": "./src/index.ts"}}, "scripts": {"build": "tsc --project tsconfig.json && tsc-alias -p tsconfig.json"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "^20.16.5", "tsc-alias": "^1.8.10", "typescript": "5.5.4"}, "dependencies": {"@repo/db": "workspace:*", "@repo/lib": "workspace:*", "sharp": "^0.33.5", "blurhash": "^2.0.5", "drizzle-orm": "^0.36.0"}}