{"$schema": "https://turbo.build/schema.json", "globalEnv": ["TURBO_TOKEN=turbo-token"], "ui": "tui", "remoteCache": {"enabled": true}, "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["src/**/*.*"], "outputs": ["dist/**"], "cache": true}, "lint": {"dependsOn": ["^lint"]}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV=development"]}, "schedule": {"cache": false, "persistent": false}, "cmd": {"cache": false, "persistent": false}, "db:studio": {"cache": false, "persistent": false}, "start": {"cache": false, "persistent": false}}}