import { aliasedTable, relations, sql } from "drizzle-orm";
import {
	pgTable,
	uuid,
	varchar,
	timestamp,
	boolean,
	date,
	pgEnum,
	text,
	numeric,
	integer,
	PgTransaction,
	json
} from "drizzle-orm/pg-core";
import { filesTable, users } from "./common.js";

export const notificationTypeEnum = pgEnum("notification_type_enum", ["user_data"]);

export const notificationsTable = pgTable("notifications", {
	id: uuid("id").primaryKey().defaultRandom(),
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description").notNull().default(""), // Not needed
	read: boolean("read").default(false),
	type: notificationTypeEnum("type").array().notNull().default([]), // chat , listing , friend , default
	url: text("url"),
	image: text("image"),
	userId: uuid("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	data: json("data").default({}),
	createdAt: timestamp("created_at").defaultNow()
});
