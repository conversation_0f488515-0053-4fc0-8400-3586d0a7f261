import { Redis, redisClient } from "@repo/redis";

export class RedisUserTracker {
  private readonly redis: Redis;
  private readonly activeUsersPrefix = "active_users:";

  constructor() {
    this.redis = redisClient;
  }

  async addUserToRoom(roomId: string, userId: string): Promise<void> {
    await this.redis.sadd(`${this.activeUsersPrefix}${roomId}`, userId);
  }

  async isRoomEmpty(roomId: string): Promise<boolean> {
    const roomSize = await this.redis.scard(
      `${this.activeUsersPrefix}${roomId}`
    );
    return roomSize === 0;
  }

  async removeUserFromRoom(roomId: string, userId: string): Promise<void> {
    await this.redis.srem(`${this.activeUsersPrefix}${roomId}`, userId);
    // Clean up empty rooms
    const roomSize = await this.redis.scard(
      `${this.activeUsersPrefix}${roomId}`
    );
    if (roomSize === 0) {
      await this.redis.del(`${this.activeUsersPrefix}${roomId}`);
    }
  }

  async getActiveUsersInRoom(roomId: string): Promise<string[]> {
    return await this.redis.smembers(`${this.activeUsersPrefix}${roomId}`);
  }

  async isUserActiveInRoom(roomId: string, userId: string): Promise<boolean> {
    const flag = await this.redis.sismember(
      `${this.activeUsersPrefix}${roomId}`,
      userId
    );
    return flag === 1;
  }
}
