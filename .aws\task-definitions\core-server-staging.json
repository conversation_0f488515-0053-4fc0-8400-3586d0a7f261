{"family": "core-server-staging", "networkMode": "awsvpc", "executionRoleArn": "arn:aws:iam::517695827507:role/ECSTaskExecutionRole", "taskRoleArn": "arn:aws:iam::517695827507:role/ECSTaskRole", "containerDefinitions": [{"name": "core-server-staging", "image": "517695827507.dkr.ecr.us-east-1.amazonaws.com/core-server-staging", "cpu": 512, "memory": 1024, "essential": true, "portMappings": [{"containerPort": 3000, "hostPort": 3000, "protocol": "tcp"}], "secrets": [{"name": "COOKIE_DOMAIN", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:COOKIE_DOMAIN::"}, {"name": "ACCESS_TOKEN_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:ACCESS_TOKEN_SECRET::"}, {"name": "REFRESH_TOKEN_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:REFRESH_TOKEN_SECRET::"}, {"name": "NODE_ENV", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:NODE_ENV::"}, {"name": "PORT", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:PORT::"}, {"name": "SERVER_URL", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:SERVER_URL::"}, {"name": "DATABASE_URL", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:DATABASE_URL::"}, {"name": "SENDER_MAIL", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:SENDER_MAIL::"}, {"name": "SMTP_HOST", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:SMTP_HOST::"}, {"name": "SMTP_USER", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:SMTP_USER::"}, {"name": "SMTP_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:SMTP_PASSWORD::"}, {"name": "SMTP_PORT", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:SMTP_PORT::"}, {"name": "SMTP_MAIL_ENCRYPTION", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:SMTP_MAIL_ENCRYPTION::"}, {"name": "AWS_ACCESS_KEY_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:AWS_ACCESS_KEY_ID::"}, {"name": "AWS_SECRET_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:AWS_SECRET_ACCESS_KEY::"}, {"name": "AWS_REGION", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:AWS_REGION::"}, {"name": "AWS_BUCKET", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:AWS_BUCKET::"}, {"name": "REDIS_HOST", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:REDIS_HOST::"}, {"name": "REDIS_PORT", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:REDIS_PORT::"}, {"name": "REDIS_USERNAME", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:REDIS_USERNAME::"}, {"name": "REDIS_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:REDIS_PASSWORD::"}, {"name": "NODE_OPTIONS", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:NODE_OPTIONS::"}, {"name": "GOOGLE_MAPS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:GOOGLE_MAPS_KEY::"}, {"name": "FIREBASE_PROJECT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:FIREBASE_PROJECT_ID::"}, {"name": "FIREBASE_CLIENT_EMAIL", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:FIREBASE_CLIENT_EMAIL::"}, {"name": "FIREBASE_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:517695827507:secret:env-staging-gow6eN:FIREBASE_PRIVATE_KEY::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/core-server-staging", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 10}}], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024"}