import PlusIcon from '@assets/svgs/plus-icon.svg';
import TickPolygonIcon from '@assets/svgs/tick-polygon-icon.svg';
import {Button, Text, View} from '@components/native';
import {ScreenWrapper} from '@components/shared';
import {BounceTap} from '@components/shared/animated';
import {useTheme} from '@context/index';
import {router} from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import React, {useState} from 'react';
import {Dimensions, Image, Platform, Pressable, ScrollView} from 'react-native';
import {SimpleGrid} from 'react-native-super-grid';
import RNFetchBlob from 'rn-fetch-blob';
import {useJournalStore} from './store/journal-store';
let MOCK_MY_JOURNALS = [
  {
    id: '1',
    type: 'Gratitude',
    bg: require('@assets/images/all-journal-bg.png'),
  },
  {
    id: '2',
    type: 'Lost',
    bg: require('@assets/images/lost-journal-bg.png'),
  },
  {
    id: '3',
    type: 'Love',
    bg: require('@assets/images/gratitude-journal-bg.png'),
  },
  {
    id: '4',
    type: 'Anger',
    bg: require('@assets/images/lost-journal-bg.png'),
  },
  {
    id: '5',
    type: 'Love',
    bg: require('@assets/images/gratitude-journal-bg.png'),
  },
  {
    id: '6',
    type: 'Anger',
    bg: require('@assets/images/lost-journal-bg.png'),
  },
];

const MAX_JOURNAL_TYPES = 9;

if (MOCK_MY_JOURNALS.length < MAX_JOURNAL_TYPES) {
  MOCK_MY_JOURNALS = [
    ...MOCK_MY_JOURNALS,
    {
      type: 'add-journal',
      bg: '',
      id: '',
    },
  ];
}

const {width} = Dimensions.get('screen');
const CARD_WIDTH = width / 3 - 50;

export const ChooseJournalType = () => {
  const {chosenJournalTypeId, tokens, setChosenJournalTypeId} = useJournalStore();
  const {mutateAsync} = useStitch('createJournal');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const onSaveClick = async () => {
    try {
      const formData = new FormData();
      formData.append('title', MOCK_MY_JOURNALS[selectedIndex].type);
      const parsedTokens = tokens.map(token => ({...token, ref: null, asset: null}));
      formData.append('description', JSON.stringify(parsedTokens));
      formData.append('category', chosenJournalTypeId);
      if (tokens.find(token => token.type === 'image-file')) {
        const file = tokens.find(token => token.type === 'image-file')!.asset!;
        const uri = Platform.OS == 'ios' ? file.uri!.replace('file://', '') : file.uri!;
        const response = await RNFetchBlob.fs.readFile(uri, 'base64');
        const blob = {
          uri: uri,
          name: file.fileName,
          type: file.type,
          data: response,
          filePath: 'asadcaslkdjasklda',
        };
        formData.append('media', blob as any);
      }

      const res = await mutateAsync(formData);
      console.log('Response Got >>>', res?.data);
    } catch (error) {
      console.log('Create Jorunal Error ', error);
    }
  };

  return (
    <ScreenWrapper title="My Journal">
      <View flexCenterColumn flex={0.8}>
        <View w={CARD_WIDTH + 10} h={CARD_WIDTH * 1.3} btr={20} bbr={20} bc="neutral30" bw={1}>
          <Image
            style={{
              height: '100%',
              width: '100%',
              borderTopRightRadius: 20,
              borderBottomRightRadius: 20,
            }}
            source={MOCK_MY_JOURNALS[selectedIndex].bg}
          />
          <View pos="absolute" left={'60%'} top={'20%'}>
            <TickPolygonIcon />
          </View>
        </View>
        <View flexCenterRow mt={25}>
          <Text fw="600" fs="20" color="neutral70" ff="PlayfairDisplay-Medium">
            Save to{' '}
          </Text>
          <Text fw="600" fs="20" color="neutral90" ff="PlayfairDisplay-SemiBold">
            {MOCK_MY_JOURNALS[selectedIndex].type} Journal ?
          </Text>
        </View>
      </View>
      <View flex={1.2} btl={30} btr={30} bg="orange" display="flex" px={20} py={30} jc="space-between">
        <Text fw="500">Choose a journal to save</Text>
        <ScrollView>
          <SimpleGrid
            listKey={'journal-type'}
            itemDimension={CARD_WIDTH}
            data={MOCK_MY_JOURNALS}
            renderItem={({item, index}) => {
              if (item.type == 'add-journal') {
                return <AddJournalButton />;
              }
              return (
                <Pressable
                  key={index}
                  onPress={() => {
                    setSelectedIndex(index);
                    setChosenJournalTypeId(item.id);
                  }}>
                  <View mr={5} mb={5} btr={20} bbr={20} bw={selectedIndex == index ? 2 : 0} bc={selectedIndex == index ? 'positive60' : 'transparent'} h={CARD_WIDTH * 1.2} bg="lightBlue">
                    <Image
                      style={{
                        position: 'absolute',
                        objectFit: 'cover',
                        top: 0,
                        left: 0,
                        height: '100%',
                        width: '100%',
                        borderTopRightRadius: 20,
                        borderBottomRightRadius: 20,
                      }}
                      source={item.bg}
                    />
                  </View>
                </Pressable>
              );
            }}
          />
        </ScrollView>
        <Button h={46} onPress={onSaveClick}>
          Save
        </Button>
      </View>
    </ScreenWrapper>
  );
};

const AddJournalButton = () => {
  const {colors} = useTheme();
  return (
    <Pressable onPress={() => router.navigate('CreateJournalType')}>
      <View btr={20} bbr={20} h={CARD_WIDTH * 1.2} bg="lightBlue" flexCenterRow>
        <BounceTap>
          <View bc="purpleLight" bw={2} mr={5} mb={5} br={100} flexCenterRow size={CARD_WIDTH / 1.5} bg="purple100">
            <PlusIcon fill={colors.purple500} />
          </View>
        </BounceTap>
      </View>
    </Pressable>
  );
};
