export interface Coordinates {
  lat: string;
  lng: string;
}

export async function getCoordinates({
  city,
  state,
  country,
}: {
  city?: string | null;
  state?: string | null;
  country?: string | null;
}): Promise<Coordinates | null> {
  try {
    const address = encodeURIComponent([city, state, country].join(","));
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${address}&key=${process.env.GOOGLE_MAPS_KEY}`;

    const response = await fetch(url);
    const data = await response.json();
    if (data.status === "OK" && data.results.length > 0) {
      return data.results[0].geometry.location;
    }

    console.error("Geocoding failed:", data.status, data.error_message);
    return null;
  } catch (error) {
    console.error("Error fetching coordinates:", error);
    return null;
  }
}
