// import React, { useEffect, useRef } from 'react';
// import { StyleSheet, View } from 'react-native';
// import { PanGestureHandler, PanGestureHandlerGestureEvent } from 'react-native-gesture-handler';
// import Animated, {
//     runOnJS,
//     useAnimatedStyle,
//     useSharedValue,
//     withSpring,
// } from 'react-native-reanimated';

import { RefreshControl } from "react-native";
import Animated from "react-native-reanimated";

// interface PullToRefreshWrapperProps {
//   children: React.ReactNode;
//   onRefresh: () => void;
//   isRefetching: boolean;
//   threshold?: number;
//   minHeight?: number;
//   maxHeight?: number;
// }

// export const PullToRefreshWrapper: React.FC<PullToRefreshWrapperProps> = ({
//   children,
//   onRefresh,
//   isRefetching,
//   threshold = -100,
//   minHeight = 50,
//   maxHeight = 100,
// }) => {
//   const translateY = useSharedValue(0);
//   const isPulling = useSharedValue(false);
//   const hasTriggeredRefresh = useSharedValue(false);
//   const scrollOffset = useRef(0);

//   // Handle pan gesture
//   const onGestureEvent = (event: PanGestureHandlerGestureEvent) => {
//     'worklet';
//     const { translationY, velocityY } = event.nativeEvent;

//     // Only allow pulling if at the top (scrollOffset = 0)
//     if (scrollOffset.current <= 0) {
//       if (translationY >= 0) {
//         // Pulling down
//         isPulling.value = true;
//         // Apply resistance effect (logarithmic scaling)
//         const resistance = Math.max(0, translationY / 2);
//         translateY.value = Math.min(resistance, maxHeight);

//         // Check if threshold reached
//         if (resistance <= threshold && !hasTriggeredRefresh.value && !isRefetching) {
//           hasTriggeredRefresh.value = true;
//           runOnJS(onRefresh)();
//         }
//       } else {
//         // Pulling up (scrolling down)
//         isPulling.value = false;
//         translateY.value = withSpring(0, { damping: 20, stiffness: 150 });
//       }
//     } else {
//       // Not at top, reset
//       isPulling.value = false;
//       translateY.value = withSpring(0, { damping: 20, stiffness: 150 });
//     }
//   };

//   // Reset when gesture ends
//   const onGestureEnd = () => {
//     'worklet';
//     if (!isRefetching) {
//       isPulling.value = false;
//       translateY.value = withSpring(0, { damping: 20, stiffness: 150 });
//       hasTriggeredRefresh.value = false;
//     }
//   };

//   // Handle isRefetching changes
//   useEffect(() => {
//     if (!isRefetching) {
//       // Hide when refetching is complete
//       translateY.value = withSpring(0, { damping: 20, stiffness: 150 });
//       isPulling.value = false;
//       hasTriggeredRefresh.value = false;
//     } else {
//       // Keep visible during refetching
//       translateY.value = withSpring(maxHeight, { damping: 20, stiffness: 150 });
//     }
//   }, [isRefetching, maxHeight]);

//   // Animated style for refresh component
//   const refreshStyle = useAnimatedStyle(() => ({
//     height: withSpring(
//       isRefetching || isPulling.value ? Math.max(minHeight, translateY.value) : 0,
//       { damping: 20, stiffness: 150 }
//     ),
//     opacity: isRefetching || isPulling.value ? 1 : 0,
//   }));

//   // Track scroll position (assuming children is a scrollable component)
//   const handleScroll = (event: any) => {
//     scrollOffset.current = event.nativeEvent.contentOffset.y;
//     if (scrollOffset.current > 0 && isRefetching) {
//       // Hide if scrolling down while refetching
//       translateY.value = withSpring(0, { damping: 20, stiffness: 150 });
//       isPulling.value = false;
//     }
//   };

//   return (
//     <View style={styles.container}>
//       <PanGestureHandler
//         onGestureEvent={onGestureEvent}
//         onEnded={onGestureEnd}
//         enabled={!isRefetching || scrollOffset.current <= 0}
//       >
//         <Animated.View style={styles.wrapper}>
//           <Animated.View style={[styles.refreshContainer, refreshStyle]}>
//             {/* Customize your refresh UI here */}
//             <View style={styles.refreshContent}>
//               <Animated.Text style={styles.refreshText}>
//                 {isRefetching ? 'Refreshing...' : 'Pull to Refresh'}
//               </Animated.Text>
//             </View>
//           </Animated.View>
//           <View onScroll={handleScroll} style={styles.content}>
//             {children}
//           </View>
//         </Animated.View>
//       </PanGestureHandler>
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//   },
//   wrapper: {
//     flex: 1,
//   },
//   refreshContainer: {
//     width: '100%',
//     backgroundColor: '#f0f0f0',
//     justifyContent: 'center',
//     alignItems: 'center',
//     overflow: 'hidden',
//   },
//   refreshContent: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   refreshText: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: '#333',
//   },
//   content: {
//     flex: 1,
//   },
// });



interface PullToRefreshWrapperProps {
    children: React.ReactNode;
    onRefresh: () => void;
    isRefetching: boolean;
    threshold?: number;
}

export const PullToRefreshWrapper: React.FC<PullToRefreshWrapperProps> = ({ children, isRefetching, onRefresh, threshold }) => {
  return (
    <Animated.ScrollView onScroll={(event) => {
      const offsetY = event.nativeEvent.contentOffset.y;
      if (offsetY < (threshold ?? -100) && !isRefetching) {
        onRefresh();
      }
    }}
      scrollEventThrottle={16}
      contentContainerStyle={{ flexGrow: 1 }}
      refreshControl={
        <RefreshControl
          refreshing={isRefetching}
          onRefresh={onRefresh}
          tintColor="#000" // Customize the color of the spinner
          title="Pull to refresh"
          titleColor="#000" // Customize the title color
          colors={['#000']} // Customize the spinner color
          progressBackgroundColor="#fff" // Customize the background color of the spinner
        />
      }
    >
      {children}

    </Animated.ScrollView>
  )
}