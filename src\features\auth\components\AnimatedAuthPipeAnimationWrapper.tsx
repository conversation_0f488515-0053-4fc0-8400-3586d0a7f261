import AuthPipe from '@assets/svgs/auth-top-pipe.svg';
import {View} from '@components/native';
import {MotiVie<PERSON>} from 'moti';
import React from 'react';
import {Keyboard, TouchableWithoutFeedback} from 'react-native';

const AnimatedAuthPipeAnimationWrapper = ({children}: {children: React.ReactNode}) => {
  return (
    <>
      <MotiView
        from={{translateY: 0}}
        animate={{translateY: [0, 20, 0, -20, 0]}}
        transition={{
          loop: true,
          type: 'timing',
          duration: 2000,
          delay: 0,
        }}>
        <View
          pos="absolute"
          top={220}
          right={1}>
          <AuthPipe />
        </View>
      </MotiView>

      <MotiView
        from={{translateY: 0}}
        animate={{translateY: [0, 20, 0, -20, 0]}}
        transition={{
          loop: true,
          type: 'timing',
          duration: 2000,
          delay: 200,
        }}>
        <View
          pos="absolute"
          top={20}
          style={{transform: [{rotate: '180deg'}]}}
          left={-5}>
          <AuthPipe />
        </View>
      </MotiView>

      <MotiView
        from={{translateY: 0}}
        animate={{translateY: [0, 20, 0, -20, 0]}}
        transition={{
          loop: true,
          type: 'timing',
          duration: 2000,
          delay: 200,
        }}>
        <View
          pos="absolute"
          top={20}
          style={{transform: [{rotate: '-90deg'}]}}
          right={-20}>
          <AuthPipe />
        </View>
      </MotiView>

      <MotiView
        from={{translateY: 0}}
        animate={{translateY: [0, 20, 0, -20, 0]}}
        transition={{
          loop: true,
          type: 'timing',
          duration: 2000,
          delay: 300,
        }}>
        <View
          pos="absolute"
          top={220}
          style={{transform: [{rotate: '290deg'}]}}
          left={-20}>
          <AuthPipe />
        </View>
      </MotiView>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>{children}</TouchableWithoutFeedback>
    </>
  );
};

export default AnimatedAuthPipeAnimationWrapper;
