// import AppLogo from '@assets/svgs/app-logo.svg';
// import CurveThread from '@assets/svgs/pre-auth-curve-thread.svg';
// import TopBgCurve from '@assets/svgs/pre-auth-top-bg-curve.svg';
// import {Button, Text, View} from '@components/native';
// import {StaggeredMotiFadeInAnim} from '@components/shared/animated';
// import {StackBackButton} from '@components/shared/StackBackButton';
// import {router} from '@navigation/refs/navigation-ref';
// import {MotiView} from 'moti';
// import React from 'react';
// import {useWindowDimensions} from 'react-native';
// import {useSafeAreaInsets} from 'react-native-safe-area-context';

// export const PreAuthScreen = () => {
//   const {height, width} = useWindowDimensions();
//   const {top} = useSafeAreaInsets();

//   return (
//     <View flex={1}>
//       {router.canGoBack() && <StackBackButton />}
//       <View h={height * 0.2} pos="absolute" top={0} w={width} left={0} style={{backgroundColor: '#fce8e8'}} />
//       <View pos="absolute" top={-40}>
//         <MotiView
//           from={{translateY: 100}}
//           animate={{translateY: -50}}
//           transition={{
//             type: 'timing',
//             duration: 10000,
//             loop: true,
//             repeatReverse: true,
//           }}>
//           <TopBgCurve width={width} height={height * 0.65} fill="#fce8e8" />
//         </MotiView>
//         <View top={-20}>
//           <MotiView
//             from={{translateY: 100}}
//             animate={{translateY: -50}}
//             transition={{
//               type: 'timing',
//               duration: 10000,
//               loop: true,
//               repeatReverse: true,
//             }}>
//             <CurveThread />
//           </MotiView>
//         </View>
//       </View>
//       <View flex={1}>
//         <View flexCenterColumn pt={120} gap={20}>
//           <StaggeredMotiFadeInAnim>
//             <AppLogo width={80} height={90} />
//             <Text fw="500" color="neutral80" ff="PlayfairDisplay-Medium" fs="26">
//               Welcome to Stitch
//             </Text>
//           </StaggeredMotiFadeInAnim>
//         </View>
//       </View>
//       <View pos="absolute" bottom={40} flexCenterColumn w={width} disableSizeMatter gap={20} px={20}>
//         <Text color="neutral80" fw="500" fs="24">
//           Heal, Connect, Track!
//         </Text>
//         <Text ta="center" fs="16">
//           Heal, Connect, Track! Heal, Connect, Track!Heal, Connect, Track!
//         </Text>
//         <View h={5} />
//         <Button
//           fw="500"
//           onPress={() => {
//             router.navigate('Signup');
//           }}>
//           SIGN UP
//         </Button>
//         <View flexCenterRow gap={3} pt={10}>
//           <Text fs="12" color="neutral70">
//             Already have an account?
//           </Text>
//           <Text
//             onPress={() => {
//               router.navigate('Login');
//             }}
//             fs="12"
//             style={{color: '#8E97FD'}}>
//             LOG IN
//           </Text>
//         </View>
//       </View>
//     </View>
//   );
// };
