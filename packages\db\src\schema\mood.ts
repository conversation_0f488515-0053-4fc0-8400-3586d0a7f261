import { pgTable, uuid, text, timestamp, varchar, boolean } from "drizzle-orm/pg-core";
import { users } from "./common.js";

export const moodTable = pgTable("mood_table", {
  id: uuid("id").primaryKey().defaultRandom(),
  value: varchar("value", { length: 255 }).notNull(), //sad, angry,
  description: text("description").default(""),
  timestamp: timestamp("timestamp", { withTimezone: true }).defaultNow(), //time of the day
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  lastOfDay: boolean()
});

//
