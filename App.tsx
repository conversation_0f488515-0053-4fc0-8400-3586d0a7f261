import {BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import {QueryClient, QueryClientProvider} from '@tanstack/react-query';
import React from 'react';
import 'react-native-gesture-handler';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import 'react-native-reanimated';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {enableScreens} from 'react-native-screens';
import {AuthProvider, NotifierProvider, ThemeProvider} from './src/context';
import StitchApp from './src/navigation/StitchApp';
import './src/packages/db/init-db.native';

enableScreens(); // Better memory usage
const queryClient = new QueryClient();

function composeProviders(...providers: React.JSXElementConstructor<{children: React.ReactNode}>[]) {
  return providers.reduce(
    (AccumulatedProviders, CurrentProvider) =>
      ({children}) =>
        (
          <AccumulatedProviders>
            <CurrentProvider>{children}</CurrentProvider>
          </AccumulatedProviders>
        ),
    ({children}) => <>{children}</>,
  );
}

const AppProviderTree = composeProviders(
  SafeAreaProvider,
  ThemeProvider,
  props => <QueryClientProvider client={queryClient} {...props} />,
  GestureHandlerRootView,
  BottomSheetModalProvider,
  AuthProvider,
  NotifierProvider,
);

export default function App() {
  return (
    <AppProviderTree>
      <StitchApp />
    </AppProviderTree>
  );
}
