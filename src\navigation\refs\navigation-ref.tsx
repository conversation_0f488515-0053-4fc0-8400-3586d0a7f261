import {CommonActions, createNavigationContainerRef, DrawerActions, StackActions, TabActions} from '@react-navigation/native';
import SCREENS from '@src/SCREENS';
import type {RootNavigatorParamList} from '../types';

export const navigationRef = createNavigationContainerRef<RootNavigatorParamList>();

export type TabScreenNames = typeof SCREENS.FEED | typeof SCREENS.JOURNAL | typeof SCREENS.MOOD_TRACKER | typeof SCREENS.PROFILE | typeof SCREENS.CONVERSATIONS;

/**
 * Custom navigation controller to mimic Expo Router API using imperative navigation.
 */
class Router {
  /**
   * ✅ Push a new screen on top of the stack
   * 📦 Uses: StackActions.push
   *
   * @param name - The name of the screen
   * @param params - Optional screen params
   *
   * ```tsx
   * router.push('ChatScreen', { userId: '123' });
   * ```
   */
  push<RouteName extends keyof RootNavigatorParamList>(name: RouteName, params?: RootNavigatorParamList[RouteName]) {
    if (navigationRef.isReady()) {
      navigationRef.dispatch(StackActions.push(name as string, params));
    }
  }

  /**
   * ✅ Navigate to a screen (replace current route if already focused)
   * 📦 Uses: navigationRef.navigate
   *
   * @param name - The screen name
   * @param params - Optional params
   *
   * ```tsx
   * router.navigate('HomeScreen');
   * router.navigate('PostScreen', { postId: 45 });
   * ```
   */
  navigate<RouteName extends keyof RootNavigatorParamList>(name: RouteName, params?: RootNavigatorParamList[RouteName]) {
    if (navigationRef.isReady()) {
      navigationRef.navigate(name as any, params as any);
    }
  }

  /**
   * ✅ Replace current screen with a new one
   * 📦 Uses: StackActions.replace
   *
   * ```tsx
   * router.replace('LoginScreen');
   * ```
   */
  replace<RouteName extends keyof RootNavigatorParamList>(name: RouteName, params?: RootNavigatorParamList[RouteName]) {
    if (navigationRef.isReady()) {
      navigationRef.dispatch(StackActions.replace(name as string, params));
    }
  }

  /**
   * ✅ Pop N screens off the stack
   * 📦 Uses: StackActions.pop
   *
   * ```tsx
   * router.pop();      // Go back 1 screen
   * router.pop(2);     // Go back 2 screens
   * ```
   */
  pop(count = 1) {
    if (navigationRef.isReady()) {
      navigationRef.dispatch(StackActions.pop(count));
    }
  }

  /**
   * ✅ Pop all screens and go to first screen in the stack
   * 📦 Uses: StackActions.popToTop
   *
   * ```tsx
   * router.popToTop();
   * ```
   */
  popToTop() {
    if (navigationRef.isReady()) {
      navigationRef.dispatch(StackActions.popToTop());
    }
  }

  /**
   * ❌ Not natively supported — Typically not needed.
   * Left as a placeholder if you build custom logic to "pop to" a specific route.
   */
  popTo<RouteName extends keyof RootNavigatorParamList>(name: RouteName, _params?: RootNavigatorParamList[RouteName]) {
    console.warn('popTo is not officially supported by React Navigation.');
  }

  /**
   * ✅ Go back to previous screen with data
   * 📦 Uses: CommonActions.goBack
   *
   * ```tsx
   * router.back({ key: 'value' });
   * ```
   */
  back(data = {}) {
    if (navigationRef.isReady() && navigationRef.canGoBack()) {
      // Get the current route and the previous route
      const routes = navigationRef.getState().routes;
      const currentRoute = routes[routes.length - 1];
      const previousRoute = routes[routes.length - 2];

      // Update params of the previous screen
      navigationRef.setParams({
        ...previousRoute.params,
        ...data,
      });

      // Go back to the previous screen
      navigationRef.dispatch(CommonActions.goBack());
    }
  }

  /**
   * ✅ Reset navigation stack to one screen
   * 📦 Uses: CommonActions.reset
   *
   * ```tsx
   * router.reset('HomeScreen');
   * ```
   */
  reset<RouteName extends keyof RootNavigatorParamList>(name: RouteName, params?: RootNavigatorParamList[RouteName]) {
    if (navigationRef.isReady()) {
      navigationRef.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{name: name as string, params}],
        }),
      );
    }
  }

  /**
   * ✅ Jump to a tab screen
   * 📦 Uses: TabActions.jumpTo
   *
   * ```tsx
   * router.jumpTo('ProfileTab');
   * ```
   */
  jumpTo<RouteName extends keyof RootNavigatorParamList>(name: RouteName, params?: RootNavigatorParamList[RouteName]) {
    if (navigationRef.isReady()) {
      navigationRef.dispatch(TabActions.jumpTo(name as string, params));
    }
  }

  /**
   * ✅ Open drawer if using drawer navigator
   * 📦 Uses: DrawerActions.openDrawer
   *
   * ```tsx
   * router.openDrawer();
   * ```
   */
  openDrawer() {
    if (navigationRef.isReady()) {
      navigationRef.dispatch(DrawerActions.openDrawer());
    }
  }

  /**
   * ✅ Close drawer if open
   * 📦 Uses: DrawerActions.closeDrawer
   *
   * ```tsx
   * router.closeDrawer();
   * ```
   */
  closeDrawer() {
    if (navigationRef.isReady()) {
      navigationRef.dispatch(DrawerActions.closeDrawer());
    }
  }

  /**
   * ✅ Toggle drawer open/close
   * 📦 Uses: DrawerActions.toggleDrawer
   *
   * ```tsx
   * router.toggleDrawer();
   * ```
   */
  toggleDrawer() {
    if (navigationRef.isReady()) {
      navigationRef.dispatch(DrawerActions.toggleDrawer());
    }
  }

  /**
   * ✅ Check if there's a screen to go back to
   *
   * ```tsx
   * if (router.canGoBack()) router.back();
   * ```
   */
  canGoBack(): boolean {
    return navigationRef.isReady() && navigationRef.canGoBack();
  }

  /**
   * ✅ Get info about the currently focused route
   *
   * ```tsx
   * const route = router.getCurrentRoute();
   * console.log(route?.name, route?.params);
   * ```
   */
  getCurrentRoute() {
    return navigationRef.getCurrentRoute();
  }

  /**
   * ✅ Navigate directly to a bottom tab screen
   * 🚫 Only allowed to: Feed, Journal, Mood, Profile, Conversations
   * 🔄 Clears stack and goes to specified tab
   *
   * ```ts
   * router.tabNavigate(SCREENS.PROFILE);
   * ```
   */
  tabNavigate(name: TabScreenNames) {
    if (navigationRef.isReady()) {
      try {
        navigationRef.dispatch(TabActions.jumpTo(name));
      } catch (error) {
        if (navigationRef.isReady()) {
          navigationRef.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [
                {
                  name: 'tab',
                  state: {
                    routes: [{name}],
                  },
                },
              ],
            }),
          );
        }
      }
    }
  }

  /**
   * ✅ Get the parameters of the specified route
   *
   * ```tsx
   * const { journalId } = router.params(SCREENS.EDIT_JOURNAL) || {};
   * console.log(journalId); // string | undefined
   * ```
   */
  params<RouteName extends keyof RootNavigatorParamList>(name: RouteName): RootNavigatorParamList[RouteName] | undefined {
    if (navigationRef.isReady()) {
      const route = navigationRef.getCurrentRoute();
      if (route && route.name === name) {
        return route.params as RootNavigatorParamList[RouteName] | undefined;
      }
    }
    return undefined;
  }
}

export const router = new Router();
