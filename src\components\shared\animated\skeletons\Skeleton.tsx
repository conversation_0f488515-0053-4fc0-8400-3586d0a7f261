import { useTheme } from '@context/index';
import { forwardRef, useEffect } from 'react';
import { DimensionValue, ViewStyle } from 'react-native';
import Animated, {
  Easing,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import { LinearGradient } from 'react-native-linear-gradient';
import { View, ViewProps } from '@components/native';
import { ThemeColorKeys } from '@/types/color-types';

export interface SkeletonProps extends ViewProps {
  /**
   * Start color of the gradient (defaults to neutral20)
   */
  startColor?: ThemeColorKeys;
  /**
   * End color of the gradient (defaults to neutral00)
   */
  endColor?: ThemeColorKeys;
  /**
   * Animation duration in milliseconds (defaults to 1000)
   */
  duration?: number;
}

export const Skeleton = forwardRef<Animated.View, SkeletonProps>(
  (
    {
      startColor = 'neutral20',
      endColor = 'neutral30',
      duration = 1000,
      style,
      ...viewProps
    },
    ref,
  ) => {
    const { colors } = useTheme();
    const blipProgress = useSharedValue(0);

    useEffect(() => {
      blipProgress.value = withRepeat(
        withTiming(1, {
          duration,
          easing: Easing.inOut(Easing.sin),
        }),
        -1,
        true,
      );
    }, [blipProgress, duration]);

    // Animated style for gradient blipping
    const blipStyle = useAnimatedStyle(() => {
      const opacity = interpolate(
        blipProgress.value,
        [0, 0.5, 1],
        [0.3, 0.8, 0.3],
      );
      return { opacity };
    });

    return (
      <View {...viewProps} style={[style, { overflow: 'hidden' }]} ref={ref}>
        <Animated.View style={[blipStyle, { height: '100%', width: '100%' }]}>
          <LinearGradient
            colors={[colors[startColor], colors[endColor]]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={{ height: '100%', width: '100%' }}
          />
        </Animated.View>
      </View>
    );
  },
);

Skeleton.displayName = 'Skeleton';