import {
  pgTable,
  uuid,
  text,
  timestamp,
  foreignKey,
  AnyPgColumn,
  index
} from "drizzle-orm/pg-core";
import { postsTable } from "./posts.js";
import { users } from "./common.js";
export const commentsTable = pgTable(
  "comments",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    text: text("comment_text").notNull(),
    postId: uuid("post_id")
      .notNull()
      .references(() => postsTable.id, { onDelete: "cascade" }),
    replyTo: uuid("reply_to").references((): AnyPgColumn => commentsTable.id, {
      onDelete: "cascade"
    }),
    deletedAt: timestamp("deleted_at"),
    createdAt: timestamp("created_at").defaultNow(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .$onUpdate(() => new Date())
  },
  (table) => [index("post_id_idx").on(table.postId), index("reply_to_idx").on(table.replyTo)]
);
