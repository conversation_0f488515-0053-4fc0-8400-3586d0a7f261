import TickIcon from '@assets/svgs/tick-icon.svg';
import {Text, View, ViewProps} from '@components/native';
import {BounceTap} from './animated';

type SelectableItemProps = {
  title: string;
  isSelected: boolean;
  multiSelect?: boolean;
  onPress?: () => void;
  viewProps?: ViewProps;
};

export const SelectableItem: React.FC<SelectableItemProps> = ({viewProps, onPress, isSelected = false, title, multiSelect = false}) => {
  const textProps = {
    display: 'flex',
    fd: 'row',
    ai: 'center',
    gap: multiSelect ? 10 : 0,
    bc: isSelected ? 'purple400' : 'neutral20',
    bg: isSelected ? 'purple200' : 'neutral00',
    mb: 10,
    p: 20,
    bw: 1,
    br: 10,
    ...viewProps,
  } as ViewProps;

  return (
    <BounceTap pressedScale={0.95} onPress={onPress}>
      <View {...textProps}>
        {multiSelect && (
          <View bc="neutral60" bw={1} br={4} flexCenterRow size={20} bg="background">
            {isSelected && <TickIcon />}
          </View>
        )}
        <Text color="neutral80" fw="500">
          {title}
        </Text>
      </View>
    </BounceTap>
  );
};
