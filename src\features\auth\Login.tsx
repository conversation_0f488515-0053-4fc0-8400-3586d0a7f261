import AppLogo from '@assets/svgs/app-logo.svg';
import AppleIcon from '@assets/svgs/apple-icon.svg';
import GoogleIcon from '@assets/svgs/google-icon.svg';
import {Button, Text, TextInput, View} from '@components/native';
import {BounceTap, StaggeredMotiFadeInAnim} from '@components/shared/animated';
import {StackBackButton} from '@components/shared/StackBackButton';
import {router} from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import {SignupLoginMutationBody} from '@packages/useStitch/types';
import React from 'react';
import {SubmitHandler, useForm} from 'react-hook-form';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import AnimatedAuthPipeAnimationWrapper from './components/AnimatedAuthPipeAnimationWrapper';

export const LoginScreen = () => {
  const insets = useSafeAreaInsets();
  const {isMutating, mutate, data} = useStitch('login', {
    mutationOptions: {
      onSuccess: (_, variables) => {
        router.navigate('VerifyOtp', {email: variables.email, type: 'login'});
      },
    },
  });

  const {control, handleSubmit, setValue} = useForm<SignupLoginMutationBody>({
    defaultValues: {
      email: '',
    },
  });

  const onSubmit: SubmitHandler<SignupLoginMutationBody> = data => {
    mutate({email: data.email});
  };

  return (
    <AnimatedAuthPipeAnimationWrapper>
      <View flex={1} p={20} display="flex" jc="space-evenly">
        {router.canGoBack() && <StackBackButton />}
        <View flexCenterColumn mt={insets.top} gap={20}>
          <StaggeredMotiFadeInAnim>
            <AppLogo width={80} height={90} />
            <Text fw="500" color="neutral80" ff="PlayfairDisplay-Medium" fs="26">
              Welcome Back !
            </Text>
          </StaggeredMotiFadeInAnim>
        </View>
        <View>
          <TextInput
            control={control}
            label="Email"
            bg="neutral10"
            textContentType="emailAddress"
            name="email"
            rules={{
              required: 'Email is required',
              pattern: {value: /^\S+@\S+$/i, message: 'Invalid email'},
            }}
            onSuffixIconPress={() => setValue('email', '')}
            keyboardType="email-address"
          />
          <Button mt={30} isLoading={isMutating} h={48} onPress={handleSubmit(onSubmit)}>
            LOG IN
          </Button>
        </View>
        <View>
          <View flexCenterColumn gap={20}>
            <View disableSizeMatter bg="neutral30" w={'100%'} h={1} />
            <Text fw="500" color="neutral80" fs="12">
              Or continue with
            </Text>
            <View mt={10} flexCenterRow gap={14}>
              <BounceTap onPress={() => {}}>
                <View br={100} h={45} flexCenterRow disableSizeMatter w={45} bg="purpleLight">
                  <GoogleIcon />
                </View>
              </BounceTap>
              <BounceTap onPress={() => {}}>
                <View br={100} h={45} flexCenterRow disableSizeMatter w={45} bg="purpleLight">
                  <AppleIcon />
                </View>
              </BounceTap>
            </View>
            <View mt={16} flexCenterRow>
              <Text fs="12">NOT A MEMBER ?</Text>
              <Text onPress={() => router.navigate('Signup')} fs="12" style={{color: '#8E97FD'}}>
                {' '}
                REGISTER NOW
              </Text>
            </View>
          </View>
        </View>
      </View>
    </AnimatedAuthPipeAnimationWrapper>
  );
};
