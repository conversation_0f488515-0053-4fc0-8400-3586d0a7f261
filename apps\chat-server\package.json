{"name": "chat-server", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node --conditions=production dist/index.js", "build": "tsc --project tsconfig.json && tsc-alias -p tsconfig.json", "dev": "tsx watch src/index.ts", "bun:dev": "bun --watch src/index.ts", "cmd": "tsx src/commands/index.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@repo/db": "workspace:^", "@repo/lib": "workspace:^", "@repo/redis": "workspace:^", "drizzle-orm": "^0.36.0", "jsonwebtoken": "^9.0.2", "uWebSockets.js": "github:uNetworking/uWebSockets.js#v20.51.0"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/jsonwebtoken": "^9.0.7", "@types/mocha": "^10.0.9", "@types/supertest": "^6.0.2", "globals": "^15.11.0", "nodemon": "^3.1.7", "prettier": "^3.3.3", "supertest": "^7.0.0", "tsc-alias": "^1.8.10", "tsx": "^4.19.2", "typescript": "^5.6.3"}}