import {ThemeColors} from '@/types/color-types';
import {DarkColors, LightColors} from '@constants/index';
import React, {createContext, useContext, useEffect, useMemo, useState} from 'react';
import {Appearance} from 'react-native';
import {SharedValue, useSharedValue} from 'react-native-reanimated';

export type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  colors: ThemeColors;
  isDarkMode: boolean;
  sheetValue: SharedValue<boolean>;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{children: React.ReactNode}> = ({children}) => {
  const [theme, setTheme] = useState<Theme>('light');
  const sheetValue = useSharedValue(false);

  useEffect(() => {
    const systemTheme = Appearance.getColorScheme();
    // if (systemTheme === 'dark') {
    // setTheme('dark');
    // }
  }, []);

  const colors = useMemo<ThemeColors>(() => {
    return theme === 'dark' ? DarkColors : LightColors;
  }, [theme]);

  const toggleTheme = () => {
    setTheme(prev => (prev === 'light' ? 'dark' : 'light'));
  };

  const value = useMemo(() => ({theme, toggleTheme, colors, isDarkMode: theme == 'dark', sheetValue}), [theme, toggleTheme, sheetValue, colors]);

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
