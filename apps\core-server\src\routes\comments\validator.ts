import vine from "@vinejs/vine";

export const createCommentValidator = vine.object({
  text: vine.string(),
  postId: vine.string(),
  replyTo: vine.string().optional(),
});

export const editCommentValidator = vine.object({
  text: vine.string(),
});

export const getCommentsValidator = vine.object({
  postId: vine.string().optional(),
  replyTo: vine.string().optional(),
  page: vine.string().optional(),
  limit: vine.string().optional(),
});
