import { bodyValidator, mediaBodyValidator } from "@/lib/validation-module.js";
import db from "@repo/db";
import {
  avatarTable,
  filesTable,
  postFilesTable,
  postsTable,
  userDetails,
} from "@repo/db/schema";
import type { Request, Response, NextFunction } from "express";
import { createPostValidator, editPostValidator } from "./validator.js";
import { Infer } from "@vinejs/vine/types";
import { MediaModule } from "@repo/bin-packages";
import { and, count, desc, eq, isNull, sql } from "drizzle-orm";
import { Paginator } from "@/lib/pagination-module.js";
import { ErrorResponse } from "@/lib/error-handlers-module/index.js";
export class PostsController {
  @mediaBodyValidator(createPostValidator)
  static async createPost(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof createPostValidator>;
      if (!req.files?.media) {
        res.status(400).json({ message: "Please upload a photo" });
        return;
      }
      // throw new Error("Just testing");
      const filesToUpload =
        req.files.media instanceof Array ? req.files.media : [req.files.media];

      const post = await db.transaction(async (tx) => {
        const files = await MediaModule.uploadPostsMedia(
          req.user.id,
          filesToUpload,
          "posts",
        );
        const [post] = await tx
          .insert(postsTable)
          .values({
            userId: req.user.id,
            body: payload.body,
            title: payload.title,
          })
          .returning();

        await tx.insert(postFilesTable).values(
          files.map((f, i) => ({
            fileId: f.id,
            userId: req.user.id,
            postId: post!.id,
            order: i + 1,
          })),
        );
        return post;
      });

      res
        .status(201)
        .json({ message: "Post created successfully", data: { post } });
    } catch (error) {
      next(error);
    }
  }

  static async getPosts(req: Request, res: Response, next: NextFunction) {
    try {
      const { limit, offset } = Paginator.getPage(req.query);
      const filter = and(isNull(postsTable.deletedAt));
      const total = await Paginator.getTotalCount(
        db.select({ count: count() }).from(postsTable).where(filter),
      );
      const rows = await db
        .select({
          id: postsTable.id,
          title: postsTable.title,
          body: postsTable.body,
          createdAt: postsTable.createdAt,
          photos: sql<string[]>`COALESCE(array_agg(${filesTable.url}), '{}')`,
          user: {
            id: userDetails.id,
            username: userDetails.userName,
            avatar: userDetails.avatarUrl,
          },
        })
        .from(postsTable)
        .leftJoin(postFilesTable, eq(postFilesTable.postId, postsTable.id))
        .leftJoin(filesTable, eq(filesTable.id, postFilesTable.fileId))
        .leftJoin(userDetails, eq(userDetails.userId, postsTable.userId))
        .where(filter)
        .groupBy(postsTable.id, userDetails.id)
        .orderBy(desc(postsTable.updatedAt))
        .limit(limit)
        .offset(offset);
      const data = Paginator.paginate(req.query, rows, total);
      res.json({ data });
    } catch (error) {
      next(error);
    }
  }

  static async getPostById(req: Request, res: Response, next: NextFunction) {
    try {
      const postId = req.params.id as string;
      const post = await db
        .select({
          id: postsTable.id,
          title: postsTable.title,
          body: postsTable.body,
          createdAt: postsTable.createdAt,
          photos: sql<string[]>`COALESCE(array_agg(${filesTable.url}), '{}')`,
          user: {
            id: userDetails.id,
            username: userDetails.userName,
            avatar: userDetails.avatarUrl,
          },
        })
        .from(postsTable)
        .leftJoin(postFilesTable, eq(postFilesTable.postId, postsTable.id))
        .leftJoin(filesTable, eq(filesTable.id, postFilesTable.fileId))
        .leftJoin(userDetails, eq(userDetails.userId, postsTable.userId))
        .where(eq(postsTable.id, postId))
        .groupBy(postsTable.id, userDetails.id);
      res.json({ data: post });
    } catch (error) {
      next(error);
    }
  }

  static async deletePost(req: Request, res: Response, next: NextFunction) {
    try {
      const postId = req.params.id as string;
      await db
        .update(postsTable)
        .set({ deletedAt: new Date() })
        .where(eq(postsTable.id, postId));
      res.json({ message: "Post deleted successfully" });
    } catch (error) {
      next(error);
    }
  }

  @mediaBodyValidator(editPostValidator, true)
  static async editPost(req: Request, res: Response, next: NextFunction) {
    try {
      const postId = req.params.id as string;
      const payload = req.body as Infer<typeof editPostValidator>;
      await db.transaction(async (tx) => {
        // find post

        const [post] = await tx
          .select({ id: postsTable.id, deletedAt: postsTable.deletedAt })
          .from(postsTable)
          .where(eq(postsTable.id, postId));

        if (!post || post.deletedAt) {
          throw new ErrorResponse("Post not found", 404);
        }

        await tx
          .update(postsTable)
          .set({ title: payload.title, body: payload.body })
          .where(eq(postsTable.id, postId));

        if (req.files?.media) {
          const filesToUpload =
            req.files.media instanceof Array
              ? req.files.media
              : [req.files.media];
          const files = await MediaModule.uploadPostsMedia(
            req.user.id,
            filesToUpload,
            "posts",
          );
          await tx.insert(postFilesTable).values(
            files.map((f) => ({
              fileId: f.id,
              userId: req.user.id,
              postId: postId,
              order: payload.imageData.findIndex(
                (i) => i.filename === f.fileName,
              ),
            })),
          );
        }
        if (payload.oldImages) {
          await MediaModule.deletePostMedia(payload.oldImages);
        }
        await Promise.all(
          payload.imageData
            .filter((i) => i.type === "network")
            .map(async (e) => {
              return tx
                .update(postFilesTable)
                .set({ order: e.index })
                .where(
                  and(
                    eq(postFilesTable.postId, postId),
                    eq(postFilesTable.fileId, e.filename),
                  ),
                );
            }),
        );
      });
      res.json({ message: "Post updated successfully" });
    } catch (error) {
      next(error);
    }
  }
}
