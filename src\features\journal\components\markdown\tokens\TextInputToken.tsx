import {useTheme} from '@context/index';
import {TypeEnum, useJournalStore} from '@features/journal/store/journal-store';
import {forwardRef, useState} from 'react';
import {NativeSyntheticEvent, TextInput, TextInputKeyPressEventData, TextInputProps} from 'react-native';

export interface TokenTextInputProps extends TextInputProps {
  index: number;
  value: string;
  removeMe?: (myIndex: number, type: TypeEnum) => void;
  updateToken?: (myIndex: number, value: string) => void;
  goToNextToken?: (myIndex: number) => void;
}

export const TokenTextInput = forwardRef<TextInput, TokenTextInputProps>((props, ref) => {
  const {index, updateToken, goToNextToken, value, ...rest} = props;
  const [input, setInput] = useState(value);
  const {colors} = useTheme();
  const {setCurrentFocusedIndex} = useJournalStore();

  const onChangeText = (text: string) => {
    setInput(text);
    setTimeout(() => {
      updateToken?.(index, input);
    }, 50);
  };

  const onKeyPress = (e: NativeSyntheticEvent<TextInputKeyPressEventData>) => {
    // if user pressed backspace and there is empty value then call cleanup and tell parent
    // that remove my index object completely from the array.
    if (e.nativeEvent.key === 'Backspace' && input === '') {
      props.removeMe?.(index, 'text-input');
    } else if (e.nativeEvent.key == 'Enter') {
      goToNextToken?.(index);
    }
  };

  return (
    <TextInput
      key={index}
      multiline
      ref={ref}
      onChangeText={onChangeText}
      onKeyPress={onKeyPress}
      onFocus={e => setCurrentFocusedIndex(index)}
      onPress={e => setCurrentFocusedIndex(index)}
      value={input}
      scrollEnabled={false}
      {...rest}
      style={{
        fontSize: 16,
        color: colors.neutral80,
        backgroundColor: colors.neutral00,
      }}
    />
  );
});
