import path from "path";
import { addressTable } from "@repo/db/schema";
import crypto from "crypto";
import dayjs from "dayjs";
import { MIMEType } from "util";
import { Constants } from "./constants.js";
import { City, Country, State } from "country-state-city";
import { Coordinates, getCoordinates } from "./maps/maps.js";
import utc from "dayjs/plugin/utc.js";
dayjs.extend(utc);

export function __dirname() {
  if (
    !process.env.NODE_ENV ||
    process.env.NODE_ENV == "development" ||
    process.env.NODE_ENV == "test"
  ) {
    return path.resolve(process.cwd(), "../../");
  }
  return process.cwd();
}

/**
 * @description Utility class for generating random strings and numbers
 */
export class Random {
  /**
   * @description Generate a random UUID
   */
  static generateUUID() {
    return crypto.randomUUID();
  }

  /**
   * @description Generate a random string
   * @param length
   * @param encoding (default: 'hex')
   */
  static generateString(length: number, encoding: BufferEncoding = "hex") {
    return crypto.randomBytes(length).toString(encoding);
  }

  /**
   * @description Generate a random number
   * @param digits (default: 6)
   */
  static generateNumber(digits: number = 6) {
    return crypto.randomInt(10 ** (digits - 1), 10 ** digits);
  }

  static generateBoolean() {
    return crypto.randomInt(0, 2) === 1;
  }
}

export function classifyMimeType(mimeType: string): string {
  const imageMimeTypes = [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/bmp",
    "image/webp",
    "image/svg+xml",
    "image/tiff",
    "image/vnd.microsoft.icon",
  ];
  const videoMimeTypes = [
    "video/mp4",
    "video/mpeg",
    "video/ogg",
    "video/webm",
    "video/quicktime",
    "video/x-ms-wmv",
    "video/x-msvideo",
    "video/3gpp",
  ];
  const documentMimeTypes = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "text/plain",
    "text/csv",
    "application/rtf",
    "application/json",
    "application/xml",
  ];

  if (imageMimeTypes.includes(mimeType)) {
    return "image";
  }
  if (videoMimeTypes.includes(mimeType)) {
    return "video";
  }
  if (documentMimeTypes.includes(mimeType)) {
    return "document";
  }
  return "other";
}

// join address
export function joinAddress(
  address: typeof addressTable.$inferSelect,
  fields: ("line1" | "line2" | "postalCode" | "city" | "state" | "country")[]
) {
  return fields
    .map((f) => address[f])
    .filter((e) => e?.trim())
    .join(", ");
}

// get date range
export function getDateRanges(start: Date, end: Date) {
  let currentDate = dayjs(start);
  const ranges: Date[] = [start];
  while (currentDate.isBefore(end, "days")) {
    currentDate = currentDate.add(1, "days");
    ranges.push(currentDate.toDate());
  }
  return ranges;
}

export function getStatusOfDates(
  lastDate: Date,
  utcOffset: number,
  dates: Date[],
  status: string,
  reviewExists: boolean,
  nonOngoing?: boolean
) {
  if (status == "completed" && !reviewExists) {
    // Ensure dates are sorted in ascending order
    // const sortedDates = dates
    //   .map((date) => dayjs(date))
    //   .sort((a, b) => a.diff(b));
    // const lastDate = sortedDates.at(-1);

    const reviewExpiration = dayjs(lastDate)
      .subtract(utcOffset)
      .add(Constants.POST_COMPLETE_WAIT_FOR_PAYMENT, "milliseconds")
      .add(Constants.REVIEW_EXPIRATION, "milliseconds");

    // const reviewExpireOn = lastDate
    //   ?.add(Constants.REVIEW_EXPIRATION, "milliseconds")
    //   .add(1, "day");
    const daysLeft = reviewExpiration?.diff(dayjs(), "days") || 0;
    if (daysLeft < 0) {
      return { flag: "review_expired", daysLeft: 0 };
    } else {
      return { flag: "review_pending", daysLeft };
    }
  }

  if (status == "completed") return { flag: "review_done", daysLeft: null };
  if (dayjs().isBefore(dayjs(dates[0])) || nonOngoing)
    return { flag: "upcoming", daysLeft: null };
  return { flag: "ongoing", daysLeft: null };
}

export function calculateBookingDates(
  utcOffset: number,
  bookingStartDate: Date,
  bookingEndDate: Date
) {
  const bookedStartDate = dayjs(bookingStartDate)
    // .subtract(utcOffset, "minutes")
    .toDate();
  const bookedEndDate = dayjs(bookingEndDate)
    // .subtract(utcOffset, "minutes")
    .toDate();
  console.log({
    bookedStartDate,
    bookedEndDate,
    utcOffset,
    bookingStartDate,
    bookingEndDate,
  });
  // throw new Error("wait");
  return {
    bookedStartDate,
    bookedEndDate,
  };
}

export async function getLatLngCountryCodeForAddress(address: {
  city?: string | null;
  state?: string | null;
  country: string | null;
}) {
  let lat: string | undefined;
  let lng: string | undefined;
  let output: Coordinates | null;
  const country = Country.getAllCountries().find(
    (country) => country.name === address.country
  );
  output = {
    lat: country!.latitude,
    lng: country!.longitude,
  };

  if (address.state && address.city) {
    output = await getCoordinates(address);
  }
  if (output) {
    lat = output.lat;
    lng = output.lng;
  }

  const code = country?.isoCode;

  return { lat, lng, code };
}

export function getAvailableDates(startDate: Date, endDate: Date) {
  const difference = Math.floor(dayjs(startDate).diff(dayjs(), "hours") / 24);
  const currentDate =
    dayjs(startDate).isBefore(dayjs()) && difference > 0
      ? dayjs(startDate).add(difference, "days").toDate()
      : startDate;
  return getDateRanges(currentDate, endDate);
}

export function isListingFullyBooked(
  startDate: Date,
  endDate: Date,
  unavailableDates: Date[]
) {
  const availableDates = getAvailableDates(startDate, endDate);
  return availableDates.every((a) => {
    return unavailableDates.some((u) => a.getTime() === u.getTime());
  });
}

export function getAutoCancelDate(
  dates: Date[],
  utcOffset: number
): Date | undefined {
  const today = dayjs();
  dates = dates.sort((a, b) => a.getTime() - b.getTime());
  for (const date of dates) {
    if (dayjs(date).diff(today, "days") < 3) {
      return dayjs(date).endOf("day").subtract(utcOffset, "minutes").toDate();
    }
  }
  return undefined;
}

export function isInRange(
  lat: number,
  lng: number,
  centerLat: number,
  centerLng: number,
  radiusMeters = 300000
): boolean {
  const R = 6371000; // Earth's radius in meters
  const toRad = (value: number) => (value * Math.PI) / 180;

  const dLat = toRad(lat - centerLat);
  const dLng = toRad(lng - centerLng);

  const a =
    Math.sin(dLat / 2) ** 2 +
    Math.cos(toRad(centerLat)) * Math.cos(toRad(lat)) * Math.sin(dLng / 2) ** 2;
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  const distance = R * c;
  return distance <= radiusMeters;
}

export function nullableFunction<T extends (...args: any[]) => any>(
  fn: T
): (...args: Parameters<T>) => ReturnType<T> | null {
  return (...args: Parameters<T>): ReturnType<T> | null => {
    if (args.some((arg) => arg === undefined || arg === null)) {
      return null;
    }
    return fn(...args);
  };
}

export function isBeforeToday(date: Date, utcOffset: number) {
  const userOffset = utcOffset / 60;
  const nowInUserOffset = dayjs().utcOffset(userOffset);
  const availableTillInUserOffset = dayjs(date).utcOffset(userOffset);
  return availableTillInUserOffset.isBefore(nowInUserOffset);
}
