import {<PERSON><PERSON>, SafeAreaView, Text, View} from '@components/native';
import {SelectableItem, StackBackButton} from '@components/shared';
import {router} from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import React, {useRef, useState} from 'react';
import {Dimensions, FlatList, ScrollView} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const onboardingConfig = {
  skipEnabled: true,
  screens: [
    {
      id: '90128s0912is',
      heading: 'What kind of pain you are going through?',
      subHeading: 'Select at least one type you would like us to help you with',
      options: [
        {id: 'partiluar_answer_id', title: 'Breakup'},
        {id: 'partiluar_answer_id', title: 'Family Loss'},
        {id: 'partiluar_answer_id', title: 'Divorce'},
        {id: 'partiluar_answer_id', title: 'Pet Loss'},
        {id: 'partiluar_answer_id', title: 'Financial Loss'},
        {id: 'partiluar_answer_id', title: 'Lost Someone'},
        {id: 'partiluar_answer_id', title: 'Dont want to share'},
      ],
      multiSelect: true,
      maxItemOnScreen: 4,
    },
    {
      id: '90128s0912is',
      heading: 'Time since the loss occurred',
      subHeading: 'Select at least one type you would like us to help you with',
      options: [
        {id: 'partiluar_answer_id', title: 'Less than a month'},
        {id: 'partiluar_answer_id', title: 'Less than 3 months'},
        {id: 'partiluar_answer_id', title: 'Less than 6 months'},
        {id: 'partiluar_answer_id', title: 'Less than a year'},
      ],
      multiSelect: false,
    },
    {
      id: '90128s0912is',
      heading: 'Current Emotional State',
      subHeading: 'Select at least one type you would like us to help you with',
      options: [
        {id: 'partiluar_answer_id', title: 'Heartbroken'},
        {id: 'partiluar_answer_id', title: 'Frustrated'},
        {id: 'partiluar_answer_id', title: 'Anxious'},
        {id: 'partiluar_answer_id', title: 'Optimistic'},
        {id: 'partiluar_answer_id', title: 'Healing'},
      ],
      multiSelect: false,
    },
  ],
};

const {width} = Dimensions.get('screen');
export const Onboarding = () => {
  const {data, isError, isLoading} = useStitch('getOnboardingQuestions');

  const {top} = useSafeAreaInsets();
  const [selectedOptions, setSelectedOptions] = useState<Record<number, any>>({});
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const handleOptionSelect = (screenIndex: number, optionTitle: string, multiSelect: boolean) => {
    setSelectedOptions(prev => {
      if (multiSelect) {
        const currentSelections = prev[screenIndex] ?? [];
        if (currentSelections.includes(optionTitle)) {
          return {
            ...prev,
            [screenIndex]: currentSelections.filter((title: string) => title !== optionTitle),
          };
        }
        return {
          ...prev,
          [screenIndex]: [...currentSelections, optionTitle],
        };
      }
      return {
        ...prev,
        [screenIndex]: [optionTitle],
      };
    });
  };

  const handleNext = () => {
    if (currentIndex < onboardingConfig.screens.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      flatListRef.current?.scrollToIndex({index: nextIndex, animated: true});
    } else {
      router.tabNavigate('Conversations');
    }
  };

  const handleSkip = () => {};

  const handlePrev = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      flatListRef.current?.scrollToIndex({index: prevIndex, animated: true});
    } else {
      router.tabNavigate('Conversations');
    }
  };

  return (
    <View flex={1}>
      <SafeAreaView flex={1} pt={top}>
        {router.canGoBack() && <StackBackButton onPress={handlePrev} />}
        <FlatList
          ref={flatListRef}
          data={onboardingConfig.screens}
          horizontal
          scrollEnabled={false}
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          renderItem={({item, index: screenIndex}) => {
            return (
              <View key={screenIndex} flex={1} disableSizeMatter pt={top + 14} px={20} w={width}>
                <Text fw="500" fs="18">
                  {item.heading}
                </Text>
                <Text mt={8} color="neutral60" fs="14">
                  {item.subHeading}
                </Text>
                <View mt={20}>
                  <ScrollView showsVerticalScrollIndicator={false} style={{marginBottom: 200}}>
                    {item.options.slice(0, item.maxItemOnScreen ?? item.options.length).map((option: any, index: number) => {
                      const isSelected = item.multiSelect ? selectedOptions[screenIndex]?.includes(option.title) ?? false : selectedOptions[screenIndex]?.[0] === option.title;
                      return (
                        <SelectableItem
                          key={index}
                          title={option.title}
                          isSelected={isSelected}
                          multiSelect={item.multiSelect}
                          onPress={() => handleOptionSelect(screenIndex, option.title, item.multiSelect)}
                        />
                      );
                    })}
                  </ScrollView>
                </View>

                <View bg="background" p={20} bottom={1} left={1} right={1} pos="absolute" display="flex" fd="row" gap={10} ai="center">
                  {screenIndex == 0 && (
                    <Button bg="background" bc="purple500" bw={2} color="purple600" h={48} isHorizontalAligned onPress={() => {}}>
                      SKIP
                    </Button>
                  )}
                  <Button h={48} isHorizontalAligned onPress={handleNext}>
                    NEXT
                  </Button>
                </View>
              </View>
            );
          }}
        />
      </SafeAreaView>
    </View>
  );
};
