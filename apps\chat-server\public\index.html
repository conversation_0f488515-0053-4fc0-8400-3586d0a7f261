<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Chat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        #login-container,
        #chat-container {
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
        }

        #chat-container {
            display: none;
        }

        #messages {
            height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #eee;
        }

        .message {
            margin-bottom: 10px;
            padding: 5px;
        }

        .system-message {
            color: #666;
            font-style: italic;
        }

        .user-message .username {
            font-weight: bold;
            margin-right: 10px;
        }

        input[type="text"] {
            padding: 8px;
            margin-right: 10px;
            width: 200px;
        }

        button {
            padding: 8px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>

<body>
    <div id="login-container">
        <h2>Login</h2>
        <input type="text" id="userId" placeholder="User ID (user1 or user2)">
        <input type="text" id="roomId" placeholder="Room ID (room1 or room2)">
        <button onclick="connect()">Connect</button>
    </div>

    <div id="chat-container">
        <h2 id="room-title">Chat Room</h2>
        <div id="messages"></div>
        <div id="message-controls">
            <input type="text" id="message" placeholder="Type a message">
            <button onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
        let ws;
        const messagesDiv = document.getElementById('messages');
        const messageInput = document.getElementById('message');

        function connect() {
            const userId = document.getElementById('userId').value;
            const roomId = document.getElementById('roomId').value;

            if (!userId || !roomId) {
                alert('Please enter both user ID and room ID');
                return;
            }

            ws = new WebSocket(`ws://localhost:9001?userId=${userId}&roomId=${roomId}`);

            ws.onopen = function () {
                console.log('Connected to chat server');
                document.getElementById('login-container').style.display = 'none';
                document.getElementById('chat-container').style.display = 'block';
                document.getElementById('room-title').textContent = `Chat Room: ${roomId}`;
            };

            ws.onmessage = function (event) {
                const data = JSON.parse(event.data);
                const messageDiv = document.createElement('div');

                if (data.type === 'system') {
                    messageDiv.className = 'message system-message';
                    messageDiv.textContent = data.message;
                } else if (data.type === 'chat') {
                    messageDiv.className = 'message user-message';
                    const username = document.createElement('span');
                    username.className = 'username';
                    username.textContent = data.username + ':';
                    messageDiv.appendChild(username);
                    messageDiv.appendChild(document.createTextNode(data.message));
                }

                messagesDiv.appendChild(messageDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            };

            ws.onerror = function (error) {
                console.error('WebSocket error:', error);
                alert('Failed to connect. Please check your credentials.');
            };

            ws.onclose = function () {
                console.log('Disconnected from chat server');
                document.getElementById('login-container').style.display = 'block';
                document.getElementById('chat-container').style.display = 'none';
            };
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (message && ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    message: message
                }));
                messageInput.value = '';
            }
        }

        messageInput.addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>

</html>