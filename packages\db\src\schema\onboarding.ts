import { pgTable, uuid, text, pgEnum, timestamp, integer } from "drizzle-orm/pg-core";
import { users } from "./common.js";

export const questionTypeEnum = pgEnum("question_type_enum", ["multiple-select", "single-select"]);
export const questionsTable = pgTable("questions", {
  id: uuid("id").primaryKey().defaultRandom(),
  order: integer("order").notNull().default(1),
  description: text("description").notNull().default(""),
  title: text("title").notNull(),
  type: questionTypeEnum("type").notNull().default("single-select")
});

export const optionsTable = pgTable("options", {
  id: uuid("id").primaryKey().defaultRandom(),
  questionId: uuid("question_id")
    .notNull()
    .references(() => questionsTable.id, { onDelete: "cascade" }),
  option: text("option").notNull(),
  order: integer("order").notNull().default(1),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

export const userAnswers = pgTable("user_answers", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  questionId: uuid("question_id")
    .references(() => questionsTable.id, { onDelete: "cascade" })
    .notNull(),
  answers: uuid("answers").array().notNull().default([])
});
