# Stage 1: Build the application
FROM node:18-alpine AS builder

# Install pnpm globally
RUN apk add --no-cache \
    git \
    curl 

# Install pnpm globally
RUN curl -f https://get.pnpm.io/v6.16.js | node - add --global pnpm

RUN apk add --no-cache git

# Set the working directory in the container
WORKDIR /app
    
# Copy the monorepo root package.json and pnpm-lock.yaml to install dependencies
COPY package.json  ./

# Copy the pnpm workspace configuration
COPY pnpm-workspace.yaml ./

# Copy the entire monorepo to the container
COPY . .

# Install dependencies for the monorepo, including shared packages
RUN pnpm install 

# Build TypeScript code for all packages
RUN pnpm build --filter=chat-server

# Stage 2: Create the production image
FROM node:18-alpine

# Install pnpm globally
RUN apk add --no-cache curl && \
    curl -f https://get.pnpm.io/v6.16.js | node - add --global pnpm

RUN apk add --no-cache \
    git \
    curl \
    gcompat

# Set the working directory in the container
WORKDIR /app

# Copy necessary files from builder stage
COPY --from=builder /app/package.json /app/pnpm-lock.yaml /app/pnpm-workspace.yaml ./
COPY --from=builder /app/apps/chat-server/dist /app/apps/chat-server/dist
COPY --from=builder /app/apps/chat-server/package.json ./apps/chat-server/package.json
COPY --from=builder /app/packages/typescript-config ./packages/typescript-config
COPY --from=builder /app/packages/db ./packages/db
COPY --from=builder /app/packages/lib ./packages/lib
COPY --from=builder /app/packages/redis ./packages/redis


# Install production dependencies
RUN pnpm install --prod --frozen-lockfile

# Expose the port that the app will run on
EXPOSE 3000

# Start the application
CMD ["node","--conditions=production", "/app/apps/chat-server/dist/index.js"]