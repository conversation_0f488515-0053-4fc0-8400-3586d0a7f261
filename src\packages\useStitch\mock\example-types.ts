
type UserResponse = {
    id: string;
    name: string;
    email: string;
}

export interface ExampleAutoLoginUserResponse {
    user: UserResponse;
    accessToken: string;
    refreshToken: string;
}

export interface ExampleUpdateUserMutationData {
    email: string;
    username: string;
    password: string;
}

export interface ExampleUserSessionData {
    user: {
        avatar: string | undefined;
        name: string;
        username: string;
        bio?: string;
        location?: string;
        playlistLink?: string;
        gender: "male" | "female" | "other";
        age?: number;
        moodEnabled: boolean;
        totalPostCount: number;
        totalHealedCount: number;
    },
    accessToken: string;
}

export interface ExampleUpdateUserMutationResponse {
    email: string;
    username: string;
    password: string;
    id: string;
}

export interface ExampleProduct {
    id: string;
    name: string;
    price: number;
    description: string;
}