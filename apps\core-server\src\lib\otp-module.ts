import { HashingModule } from "./hashing-module.js";
import { Random } from "@repo/lib";
import { and, eq, gt } from "drizzle-orm";
import MockMailService from "./mail-module/mock.js";
import SMTPMailService from "./mail-module/nodemailer.js";
import db from "@repo/db";
import { otps } from "@repo/db/schema";
import { AdminEmailTemplate } from "@repo/email";
// import { AdminEmailTemplate } from "@repo/email";

/**
 * @description Handles operations related to One Time Passwords (OTPs).
 */
export class OTPModule {
  /**
   * @description Generates a random OTP and sends it to the user's email via email service.
   * @param {{ email: string; name: string }} options
   * @returns {Promise<void>}
   */
  static async sendMailOTP({ email }: { email: string }): Promise<void> {
    const otp = Random.generateNumber(4);
    const hash = await HashingModule.hash(otp.toString());
    const otpDoc = await db.query.otps.findFirst({
      where: (data, { eq }) => eq(data.email, email),
    });
    if (otpDoc) {
      await db.update(otps).set({ otp: hash }).where(eq(otps.email, email));
    } else {
      await db.insert(otps).values([{ email, otp: hash }]);
    }
    if (["test", ""].includes(process.env.NODE_ENV)) {
      // await MockMailService.sendMail({
      //   to: email,
      //   text: `your otp is ${otp}`,
      // });
    } else {
      const html = await AdminEmailTemplate.getOtpEmail({
        otp: otp.toString(),
      });
      await SMTPMailService.sendMail(
        email,
        `OTP for ${email}`,
        `your otp is ${otp}`,
        html,
      );
    }
  }

  /**
   * @description Verifies the OTP sent to the user's email.
   * @param {string} email
   * @param {string} otp
   * @param {boolean} [deleteOtp=true] whether to delete the OTP doc after verification
   * @returns {Promise<boolean>}
   */
  static async verifyMailOTP(
    email: string,
    otp: string,
    deleteOtp: boolean = false,
  ): Promise<boolean> {
    // const EXPIRATION_CONSTANT = 5 * 60 * 1000;
    // const expirationTime = new Date(Date.now() + EXPIRATION_CONSTANT).getTime();

    if (["test", "development"].includes(process.env.NODE_ENV)) return true;

    const otpDoc = await db.query.otps.findFirst({
      where: (data, { eq }) => and(eq(data.email, email)),
    });
    if (["test", "development"].includes(process.env.NODE_ENV)) return true;
    if (!otpDoc) return false;

    const verified = await HashingModule.compare(otp, otpDoc.otp);
    if (!verified) return false;
    if (deleteOtp) {
      await db.delete(otps).where(eq(otps.email, email));
    } else {
      await db
        .update(otps)
        .set({ status: "verified" })
        .where(eq(otps.email, email));
    }
    return true;
  }

  /**
   * @description Checks if the OTP has been verified.
   * @param {string} email
   * @returns {Promise<boolean>}
   */
  static async checkOTPStatus(email: string): Promise<boolean> {
    const otpDoc = await db.query.otps.findFirst({
      where: (data, { eq }) => eq(data.email, email),
    });
    if (!otpDoc) return false;
    const verified = otpDoc.status === "verified";
    if (!verified) return false;
    return true;
  }
  static async deleteOTP(email: string): Promise<boolean> {
    const otpDoc = await db.query.otps.findFirst({
      where: (data, { eq }) => eq(data.email, email),
    });
    if (!otpDoc) return false;
    await db.delete(otps).where(eq(otps.email, email));
    return true;
  }
}

function otpTemplate(otp: string) {
  return `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">

    <link
      rel="stylesheet"
      href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" />

    <title>BeMyGuest</title>


  </head>

  <style>
    @media only screen and (max-width: 600px) {
  .email_mobile {
    padding: 0 30px !important;
  }
}
  </style>
  <body style="background-color: #fff9f9; font-family: 'inter', sans-serif ">
    <table
      border="0"
      cellpadding="0"
      cellspacing="0"
      style="max-width: 800px; width: 100%; margin: 0 auto;" >
      <tr>
        <td>
          <table class="email_mobile" border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-top: 100px;">
            <tr style="background-color: white; padding: 50px">
              <td style="padding: 50px">
                <p style="margin: 0 0 16 ; font-size: 16px;"><strong>Hello</strong></p>

                <p style="margin: 0; line-height: 30px; font-size: 16px;">
                  We have received a request for Login/Signup. Your One Time
                  Password (OTP) is <strong>${otp}</strong> 
                  which is valid for 30 mins.
                </p>

                <div style="margin: 10px 0">
                  <p style="margin: 0; font-size: 16px;">
                    If you face any issues, write to us at
                    <a href="mailto:<EMAIL>" style="color: #5a24c7; text-decoration: none; font-size: 16px;"><EMAIL></a>
                  </p>
                </div>
              </td>
            </tr>

            <tr>
              <td class="">
                <p style="margin: 0; padding-top: 30px">
                  <strong style="font-size: 24px; font-weight: 600;">Regards,</strong>
                </p>
                <p style="margin: 0; padding-top: 10px;">Team BeMyGuest</p>
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <tr>
        <td style="padding-top: 100px;">
          <table
            border="0"
            cellpadding="0"
            cellspacing="0"
            style="
              width: 100%;
              padding: 10px 25px;
              padding-bottom: 30px;
              border-bottom: 2px solid rgb(0, 0, 0);
            ">
            <tr>
              <td width="100%" align="center">
                <a href="">
                  <a href="mailto:<EMAIL>"  style="color: #d06000; text-decoration: none;">Need Help?</a> Click to
                  contact us for queries
                </p>
              </td>
            </tr>
            <tr>
              <td width="50%" align="center">
                <ul style="padding: 0; margin: 0">
                  <li style="list-style: none; display: inline-block">
                    <a
                      href=" https://www.instagram.com/heyybmg?igsh=MTlpY3ZyeXo2Y3cxcA=="
                      style="
                        width: 40px;
                        height: 40px;
                        text-decoration: none;
                        margin-right: 10px;
                      ">
                      <img
                        src="https://${process.env.AWS_BUCKET}.s3.us-east-1.amazonaws.com/image/assets/instagram.png"
                        width="35"
                        height="35"
                        alt="instagram" />
                    </a>
                  </li>
                  <li style="list-style: none; display: inline-block">
                    <a
                      href="https://www.facebook.com/profile.php?id=61562958945100&mibextid=kFxxJD"
                      style="
                        width: 40px;
                        height: 40px;
                        text-decoration: none;
                        margin-right: 10px;
                      ">
                      <img
                        src="https://${process.env.AWS_BUCKET}.s3.us-east-1.amazonaws.com/image/assets/fb.png"
                        width="35"
                        height="35"
                        alt="facebook" />
                    </a>
                  </li>
                  <li style="list-style: none; display: inline-block">
                    <a
                      href=""
                      style="
                        width: 40px;
                        height: 40px;
                        text-decoration: none;
                        margin-right: 10px;
                      ">
                      <img
                        src="https://${process.env.AWS_BUCKET}.s3.us-east-1.amazonaws.com/image/assets/youtube.png"
                        width="35"
                        height="35"
                        alt="facebook" />
                    </a>
                  </li>
                </ul>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>
`;
}
