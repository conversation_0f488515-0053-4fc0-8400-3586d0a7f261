{"name": "core-server", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node --conditions=production dist/index.js", "build": "tsc --project tsconfig.json && tsc-alias -p tsconfig.json", "dev": " tsx watch src/index.ts", "bun:dev": "bun --watch src/index.ts", "cmd": "tsx src/commands/index.ts", "schedule": "tsx src/commands/schedular.ts", "test:e2e": "NODE_ENV=test mocha --config tests/.mocharc.json", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\""}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.693.0", "@repo/db": "workspace:^", "@repo/lib": "workspace:^", "@repo/email": "workspace:^", "@repo/repo": "workspace:^", "@repo/bin-packages": "workspace:^", "@vinejs/vine": "^2.1.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "blurhash": "^2.0.5", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "country-state-city": "^3.2.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "drizzle-orm": "^0.36.0", "exceljs": "^4.4.0", "express": "^4.21.1", "express-fileupload": "^1.5.1", "express-list-endpoints": "^7.1.0", "google-auth-library": "^9.15.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "morgan": "^1.10.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "semver": "^7.6.3", "sharp": "^0.33.5"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-fileupload": "^1.5.1", "@types/express-list-endpoints": "^6.0.3", "@types/jsonwebtoken": "^9.0.7", "@types/mocha": "^10.0.9", "@types/morgan": "^1.9.9", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.16", "@types/semver": "^7.5.8", "@types/supertest": "^6.0.2", "globals": "^15.11.0", "mocha": "^10.8.2", "nodemon": "^3.1.7", "prettier": "^3.3.3", "supertest": "^7.0.0", "tsc-alias": "^1.8.10", "tsx": "^4.19.2", "typescript": "^5.6.3"}}