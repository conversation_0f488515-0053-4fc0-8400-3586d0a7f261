import { and, AnyColumn, exists, SQL, sql, or, inArray, eq, notExists, not } from "drizzle-orm";
import { avatarTable, blockListTable, friendsTable, userDetails } from "./schema/common.js";
import { PgColumn } from "drizzle-orm/pg-core";
import db from "./index.js";

export const increment = (column: AnyColumn, value = 1) => {
  return sql`${column} + ${value}`;
};

export const decrement = (column: AnyColumn, value = 1) => {
  return sql`${column} - ${value}`;
};

export const push = (column: AnyColumn, value: any) => {
  return sql`array_append(${column}, ${value})`;
};

export const sqlConcat = (columns: (AnyColumn | null | undefined)[], separator: string = " ") => {
  const filteredColumns = columns
    .filter((col): col is AnyColumn => col != null)
    .map((col) => sql`TRIM(COALESCE(${col}, ''))`);

  if (filteredColumns.length === 0) {
    return sql<string>`''`;
  }

  return sql<string>`TRIM(CONCAT_WS(${separator}, ${sql.join(filteredColumns, sql`,`)}))`;
};

export const concatArray = (column: AnyColumn, values: any[]) => {
  const columnType = column.getSQLType().replace("[]", "");
  console.log(columnType);
  return sql`array_cat(${column}, ${sql`ARRAY[${sql.join(
    values.map((value) => sql`CAST(${value} AS ${sql.raw(columnType)})`),
    sql`, `
  )}]`})`;
};

export const removeFromArray = (column: AnyColumn, values: any[]) => {
  const columnType = column.getSQLType().replace("[]", ""); // Remove array brackets to get the base type
  return values.reduce(
    (acc, value) => sql`array_remove(${acc}, ${sql`CAST(${value} AS ${sql.raw(columnType)}`})`,
    column
  );
};

export function castToText(column: AnyColumn) {
  return sql`CAST(${column} AS TEXT)`;
}

type AggregateType<T> =
  | {
      aggregate: false;
      data: T;
    }
  | {
      aggregate: true;
      data: T;
    };

export function objectBuilder<T extends Record<string, PgColumn | SQL>, K extends AggregateType<T>>(
  shape: T,
  aggregate: K["aggregate"] = false,
  orderby?: any
): K["aggregate"] extends true ? SQL : SQL<T> {
  const chunks: SQL[] = [];

  Object.entries(shape).forEach(([key, value]) => {
    if (chunks.length > 0) {
      chunks.push(sql.raw(","));
    }
    chunks.push(sql.raw(`'${key}',`));
    chunks.push(sql`${value}`);
  });

  if (aggregate) {
    if (orderby) {
      console.log("order_by");
      return sql<T>`json_agg(jsonb_build_object(${sql.join(chunks)}) ORDER BY ${orderby} ASC)`;
    } else {
      return sql<T>`json_agg(jsonb_build_object(${sql.join(chunks)}))`;
    }
  }

  return sql<T>`jsonb_build_object(${sql.join(chunks)})`;
}

// export type SwitchCases<T extends string> = {
//   [key in T]?: Record<string, PgColumn | SQL>;
// };

// export type SwitchProps<T extends string> = {
//   value: T;
//   cases: SwitchCases<T>;
//   default?: Record<string, PgColumn | SQL>;
// };

// export function SwitchSql<T extends string>(props: SwitchProps<T>) {
//   const { value, cases, default: defaultCase } = props;

//   if (Object.keys(cases).length === 0) {
//     throw new Error("At least one case is required");
//   }

//   const allColumnKeys = new Set<string>();
//   Object.values(cases).forEach((entry) => {
//     if (entry) {
//       Object.keys(entry).forEach((key) => allColumnKeys.add(key));
//     }
//   });
//   if (defaultCase) {
//     Object.keys(defaultCase).forEach((key) => allColumnKeys.add(key));
//   }
//   const columnKeys = Array.from(allColumnKeys);

//   if (columnKeys.length === 0) {
//     throw new Error("No columns found in cases or default");
//   }

//   const sqlCases = columnKeys.map((columnKey) => {
//     const caseConditions: SQL[] = [];

//     for (const [caseKey, columns] of Object.entries(cases)) {
//       const columnValue = (columns as any)?.[columnKey];
//       if (columnValue) {
//         caseConditions.push(
//           sql`WHEN ${sql.raw(`'${caseKey}'`)} THEN ${columnValue}`
//         );
//       }
//     }

//     const defaultColumn = defaultCase?.[columnKey] ?? sql.raw("NULL");

//     return sql`CASE ${sql.identifier(value)} ${sql.join(caseConditions, sql.raw(" "))} ELSE ${defaultColumn} END AS ${sql.identifier(columnKey)}`;
//   });

//   return sql.join(sqlCases, sql.raw(", "));
// }

//RE
/**
 * filter
 */

// export const filterPrivateListings = (userId: string) =>
//   or(
//     eq(userDetails.userId, userId),
//     eq(userDetails.visibility, "public"),
//     and(
//       eq(userDetails.visibility, "private"),
//       exists(
//         db
//           .select()
//           .from(friendsTable)
//           .innerJoin(
//             userDetails,
//             or(
//               and(
//                 eq(friendsTable.userId, userId),
//                 eq(friendsTable.friendId, userDetails.userId),
//                 eq(friendsTable.status, "accepted")
//                 // not(eq(userDetails.userId, userId))
//               ),
//               and(
//                 eq(friendsTable.userId, userDetails.userId),
//                 eq(friendsTable.friendId, userId),
//                 eq(friendsTable.status, "accepted")
//                 // not(eq(userDetails.userId, userId))
//               )
//             )
//           )
//       )
//     )
//   );

export const filterPrivateListings = (userId: string) => {
  const profileFilter = or(
    eq(userDetails.visibility, "public"),
    and(
      eq(userDetails.visibility, "private"),
      or(eq(friendsTable.userId, userId), eq(friendsTable.friendId, userId)),
      eq(friendsTable.status, "accepted")
    ),
    eq(userDetails.userId, userId)
  );

  const friendJoin = and(
    or(
      and(eq(friendsTable.userId, userId), eq(friendsTable.friendId, userDetails.userId)),
      and(eq(friendsTable.userId, userDetails.userId), eq(friendsTable.friendId, userId))
    ),
    eq(friendsTable.status, "accepted")
  );
  return { profileFilter, friendJoin };
};

export const filterBlockedListings = (userId: string) => {
  const blockFilter = or(
    and(eq(blockListTable.userId, userId), eq(blockListTable.blockedUserId, userDetails.userId)),
    and(eq(blockListTable.blockedUserId, userId), eq(blockListTable.userId, userDetails.userId))
  );
  const blockJoin = and(
    or(
      and(eq(blockListTable.userId, userId), eq(blockListTable.blockedUserId, userDetails.userId)),
      and(eq(blockListTable.userId, userDetails.userId), eq(blockListTable.blockedUserId, userId))
    )
  );
};

export const userSelect = {
  id: userDetails.id,
  userId: userDetails.userId,
  userName: userDetails.userName,
  avatar: userDetails.avatarUrl
};
