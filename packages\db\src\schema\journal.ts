import { integer, json, pgTable, text, timestamp, uuid, varchar } from "drizzle-orm/pg-core";
import { filesTable, users } from "./common.js";

export const JournalChunkTypes = [
  "text-input",
  "image-file",
  "audio-file",
  "image-url",
  "audio-url"
];

export const journalCategoryTable = pgTable("journal_category", {
  id: uuid("id").primaryKey().defaultRandom(),
  type: varchar("type", { length: 255 }).notNull(),
  title: varchar("title", { length: 255 }).notNull(),
  bgImage: text("url").notNull(),
  userId: uuid("user_id").references(() => users.id, { onDelete: "cascade" })
});

export type JournalChunk = {
  value: string;
  type: (typeof JournalChunkTypes)[number];
};

export const journalTable = pgTable("journal", {
  id: uuid("id").primaryKey().defaultRandom(),
  title: varchar("title", { length: 255 }).notNull(),
  userId: uuid("user_id").references(() => users.id, { onDelete: "cascade" }),
  categoryId: uuid("category_id").references(() => journalCategoryTable.id, {
    onDelete: "cascade"
  }),
  deletedAt: timestamp("deleted_at"),
  description: json("description").$type<JournalChunk[]>().notNull(),
  summary: varchar("summary", { length: 255 }).notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

// export const journalFilesTable = pgTable("journal_files", {
//   id: uuid("id").primaryKey().defaultRandom(),
//   journalId: uuid("journal_id").references(() => journalTable.id, {
//     onDelete: "cascade"
//   }),
//   fileId: uuid("file_id").references(() => filesTable.id, { onDelete: "cascade" }),
//   order: integer("order").notNull().default(1),
//   preview: text("preview"),
//   createdAt: timestamp("created_at").defaultNow(),
//   updatedAt: timestamp("updated_at")
//     .defaultNow()
//     .$onUpdate(() => new Date())
// });
