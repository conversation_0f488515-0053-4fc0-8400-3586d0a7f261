import vine from "@vinejs/vine";

export const validateCreateUserDetails = vine.object({
  username: vine.string(),
  dob: vine.date({ formats: ["YYYY-MM-DD"] }).optional(),
});

export const validateUserNameQuery = vine.object({
  username: vine.string(),
});

export const validateUpdateUserDetails = vine.object({
  avatarUrl: vine.string().optional(),
  userName: vine.string().optional(),
  gender: vine.string().optional(),
  bio: vine.string().optional(),
  dob: vine.date().optional(),
  avatar: vine.any().optional(),
  city: vine.string().optional(),
  state: vine.string().optional(),
  country: vine.string().optional(),
  showMood: vine.boolean().optional(),
  playlistLink: vine.string().url().optional(),
});

export const validateGetUserNameSuggestion = vine.object({
  firstName: vine.string(),
  lastName: vine.string().optional(),
});
