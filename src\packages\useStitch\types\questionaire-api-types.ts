// {
//     "id": "0a7e2e44-3d6e-4670-9522-5995f8e15b02",
//     "title": "Current Emotional State",
//     "description": "Select at least one type you would like us to help you with",
//     "type": "single-select",
//     "options": [
//         {
//             "id": "d806dab5-d102-422a-b378-3e8be3b6db29",
//             "option": "Heartbroken"
//         },
//         {
//             "id": "a5d6ecd9-46ec-417c-b29d-5a27e1e08837",
//             "option": "Frustrated"
//         },
//         {
//             "id": "1f3fa47f-b282-4c95-ac8c-3cc16441e9dc",
//             "option": "Anxious"
//         },
//         {
//             "id": "e7415be7-a5a9-4e8c-9673-3b0315561ae1",
//             "option": "Optimistic"
//         },
//         {
//             "id": "b253359f-2d58-41ce-9e75-c7903b6a2cb7",
//             "option": "Healing"
//         }
//     ]
// },
// {
//     "id": "5f33c109-b1d7-4dc1-9413-fa90f1e0cbc7",
//     "title": "Time since the loss occurred",
//     "description": "Select at least one type you would like us to help you with",
//     "type": "single-select",
//     "options": [
//         {
//             "id": "d7543110-863b-4718-aa51-748587398f88",
//             "option": "Less than a month"
//         },
//         {
//             "id": "8128d8b2-45c2-48a7-abd2-fafe0676e9ff",
//             "option": "Less than 3 months"
//         },
//         {
//             "id": "d166417c-708e-4ef4-b628-e684438ea2a6",
//             "option": "Less than 6 months"
//         },
//         {
//             "id": "b44a297e-6db1-4472-8a47-1ba929901ae1",
//             "option": "Less than a year"
//         }
//     ]
// },
// {
//     "id": "8b809812-5288-4062-9306-943cf09dac22",
//     "title": "What kind of pain you are going through?",
//     "description": "Select at least one type you would like us to help you with",
//     "type": "multiple-select",
//     "options": [
//         {
//             "id": "30d1e14a-69b1-4dcd-a11b-2ac7129909af",
//             "option": "Breakup"
//         },
//         {
//             "id": "7bd5b8aa-b723-4f62-800c-1a3c170f4594",
//             "option": "Family Loss"
//         },
//         {
//             "id": "205458ff-8b76-4b89-95cf-d47d097b1757",
//             "option": "Divorce"
//         },
//         {
//             "id": "f5d02f79-c2cd-4ea0-97ce-23e3f21ce71c",
//             "option": "Pet Loss"
//         },
//         {
//             "id": "73bd9448-7836-4c92-bfb5-8375c4c050f8",
//             "option": "Financial Loss"
//         },
//         {
//             "id": "a3a42947-1fca-4c85-889a-f361b3c69789",
//             "option": "Lost Someone"
//         },
//         {
//             "id": "3f0ad085-1604-44ae-8283-c643c399f7db",
//             "option": "Dont want to share"
//         }
//     ]
// }

export interface QuestionOption {
  id: string;
  option: string;
}

export interface Questions {
  id: string;
  title: string;
  description: string;
  type: 'single-select' | 'multiple-select';
  options: QuestionOption[];
}

export type GetOnboardingQuestionsResponse = Questions[];
