import { Router } from "express";
import { JournalCategoriesController } from "./controller.js";

export function createJournalCategoriesRouter() {
  const router = Router();
  router.get("/list", JournalCategoriesController.getAllCategories);
  router.get("/add", JournalCategoriesController.addCategory);
  return router;
}
// https://stitched-dev-bucket.s3.us-east-1.amazonaws.com/default-journal-categories/all-journal-bg.png
// https://stitched-dev-bucket.s3.us-east-1.amazonaws.com/default-journal-categories/all-journal-bg.png
