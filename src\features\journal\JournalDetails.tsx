import DeleteIcon from '@assets/svgs/delete-icon.svg';
import PencilIcon from '@assets/svgs/pencil-icon.svg';
import ShareIcon from '@assets/svgs/share-icon.svg';
import {Text, View} from '@components/native';
import {ScreenWrapper} from '@components/shared';
import {BounceTap} from '@components/shared/animated';
import {useTheme} from '@context/index';
import {router} from '@navigation/refs/navigation-ref';
import React from 'react';

export const JournalDetails = () => {
  const {} = useTheme();
  const params = router.params('JournalDetails');
  console.log(params);

  return (
    <ScreenWrapper title={<Header />}>
      <View bg="neutral00" flex={1}>
        <Text fs="20">Hi i am normal</Text>
        <Text fs="20">Hi i am customised</Text>
      </View>
    </ScreenWrapper>
  );
};

const Header = () => {
  const params = router.params('JournalDetails');
  const {colors} = useTheme();
  return (
    <View flex={1} display="flex" fd="row" ai="center" jc="space-between">
      <View>
        <Text fw="600" color="neutral90" fs="18" numberOfLines={1}>
          {params?.type}
        </Text>
        <View size={4} />
        <Text fs="14" color="neutral40" numberOfLines={1}>
          {params?.publishedOn}
        </Text>
      </View>
      <View gap={20} display="flex" fd="row">
        <BounceTap onPress={() => {}}>
          <PencilIcon />
        </BounceTap>
        <BounceTap onPress={() => {}}>
          <ShareIcon />
        </BounceTap>
        <BounceTap>
          <DeleteIcon fill={colors.neutral60} />
        </BounceTap>
      </View>
    </View>
  );
};
