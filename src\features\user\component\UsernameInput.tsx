import TickIcon from '@assets/svgs/tick-icon.svg';
import {TextInput} from '@components/native';
import {RingSpinner} from '@components/shared/animated';
import {useTheme} from '@context/index';
import useStitch from '@packages/useStitch';
import debounce from 'lodash/debounce'; // Install lodash for debouncing
import React, {useCallback, useEffect, useState} from 'react';
import {useForm} from 'react-hook-form';
import Ionicons from 'react-native-vector-icons/Ionicons';

// Define form input type
interface FormData {
  username: string;
}

interface UsernameInputProps {
  onSuccess?: (username: string, isValid: boolean) => void;
  initialValue?: string;
}

const UsernameInput: React.FC<UsernameInputProps> = ({onSuccess, initialValue = ''}) => {
  const {colors} = useTheme();
  const {control, watch, setValue} = useForm<FormData>({
    defaultValues: {
      username: initialValue,
    },
  });
  const [isValid, setIsValid] = useState(false);
  const username = watch('username'); // Watch username field for changes

  // Debounced refetch to avoid excessive API calls
  const debouncedRefetch = useCallback(
    debounce((refetch: () => void) => {
      refetch();
    }, 500),
    [],
  );

  const {mutateAsync, isMutating} = useStitch('usernameAvailibility', {
    queryParams: {username},
    mutationOptions: {
      onSuccess: data => {
        if (data.data.available) {
          setIsValid(true);
          onSuccess?.(data.data.username, data.data.available);
        } else {
          onSuccess?.('', false);
          setIsValid(false);
        }
      },
      onError: () => {
        onSuccess?.(username, false);
        setIsValid(false);
      },
    },
  });

  // Trigger refetch on username change
  useEffect(() => {
    if (username) {
      debouncedRefetch(mutateAsync);
    } else {
      onSuccess?.('', false);
    }
  }, [username, debouncedRefetch]);

  return (
    <TextInput
      control={control}
      onChange={e => {
        setValue('username', e.nativeEvent.text);
      }}
      suffixIcon={
        isMutating ? <RingSpinner color="purple600" /> : isValid && username != '' ? <TickIcon height={30} width={18} /> : <Ionicons name="close" color={colors.negative50} size={25} />
      }
      suffixIconAlwaysVisible={true}
      name="username"
      label="Username"
      autoCorrect={false}
      autoCapitalize="none"
      keyboardType="web-search"
      bg="neutral10"
    />
  );
};

export default UsernameInput;
