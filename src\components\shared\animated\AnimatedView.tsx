import {ThemeColorKeys} from '@/types/color-types';
import {useTheme} from '@context/Theme/ThemeContext';
import Animated from 'react-native-reanimated';
import {forwardRef} from 'react';
import {DimensionValue, ViewProps as RNViewProps, View, ViewStyle} from 'react-native';
import {moderateScale, moderateVerticalScale, scale, verticalScale} from 'react-native-size-matters';

export interface AnimatedViewProps extends RNViewProps {
  /**
   * Background color enum
   */
  bg?: ThemeColorKeys;
  /**
   * Width
   */
  w?: number | DimensionValue;
  /**
   * Min height
   */
  mih?: number | DimensionValue;
  /**
   * Height
   */
  h?: number;
  /**
   * Border radius
   */
  br?: number;
  /**
   * Border width
   */
  bw?: number;
  /**
   * Border color enum
   */
  bc?: ThemeColorKeys;
  /**
   * Padding
   */
  p?: number;
  /**
   * Padding top
   */
  pt?: number;
  /**
   * Padding bottom
   */
  pb?: number;
  /**
   * Padding left
   */
  pl?: number;
  /**
   * Padding right
   */
  pr?: number;
  /**
   * Padding horizontal
   */
  px?: number;
  /**
   * Padding vertical
   */
  py?: number;
  /**
   * Margin
   */
  m?: number;
  /**
   * Margin top
   */
  mt?: number;
  /**
   * Margin bottom
   */
  mb?: number;
  /**
   * Margin left
   */
  ml?: number;
  /**
   * Margin right
   */
  mr?: number;
  /**
   * Margin horizontal
   */
  mx?: number;
  /**
   * Margin vertical
   */
  my?: number;
  /**
   * Display
   */
  display?: 'flex' | 'contents' | 'none';
  /**
   * Flex
   */
  flex?: number;
  /**
   * Flex direction
   */
  fd?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  /**
   * Center content (sets alignItems and justifyContent to center)
   */
  center?: boolean;
  /**
   * Justify content
   */
  jc?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  /**
   * Align items
   */
  ai?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';
  /**
   * Flex wrap
   */
  fw?: 'wrap' | 'nowrap' | 'wrap-reverse';
  /**
   * Shorthand for flexDirection: row
   */
  row?: boolean;
  /**
   * Shorthand for flexDirection: column
   */
  column?: boolean;
  /**
   * Shorthand for flexDirection: row, alignItems: center, justifyContent: center
   */
  flexCenterRow?: boolean;
  /**
   * Shorthand for flexDirection: column, alignItems: center, justifyContent: center
   */
  flexCenterColumn?: boolean;
  /**
   * Gap between flex items
   */
  gap?: number;

  left?: DimensionValue;
  right?: DimensionValue;
  top?: DimensionValue;
  bottom?: DimensionValue;

  /**
   * Position
   */
  pos?: 'relative' | 'absolute' | 'static' | 'sticky' | 'fixed';

  /**
   * Disables the use of react-native-size-matters completely. Default is false
   */
  disableSizeMatter?: boolean;

  /**
   * Z index
   */
  z?: number;

  /**
   * Size takes the size and creates a component with same height and width, without size matter extension
   */
  size?: number;
}

export const AnimatedView = forwardRef<View, AnimatedViewProps>(
  (
    {
      bg = 'transparent',
      w,
      h,
      br,
      bw,
      bc,
      mih,
      p,
      pt,
      pb,
      pl,
      pr,
      pos,
      px,
      py,
      m,
      mt,
      mb,
      ml,
      mr,
      mx,
      my,
      flex,
      fd,
      center,
      jc,
      ai,
      fw,
      row,
      column,
      flexCenterRow,
      flexCenterColumn,
      gap,
      style,
      display,
      top,
      bottom,
      left,
      right,
      disableSizeMatter = false,
      z,
      size,
      ...props
    },
    ref,
  ) => {
    const {colors} = useTheme();

    const defaultStyles = {
      backgroundColor: colors[bg],
      ...(w !== undefined && {
        width: disableSizeMatter ? w : !isNaN(Number(w)) ? scale(Number(w)) : w,
      }),
      ...(mih !== undefined && {
        minHeight: disableSizeMatter ? mih : !isNaN(Number(mih)) ? scale(Number(mih)) : mih,
      }),
      ...(h !== undefined && {
        height: disableSizeMatter ? h : verticalScale(h),
      }),
      ...(br !== undefined && {borderRadius: moderateScale(br, 0.5)}),
      ...(bw !== undefined && {borderWidth: moderateScale(bw, 0.5)}),
      ...(bc !== undefined && {borderColor: colors[bc]}),
      ...(p !== undefined && {padding: moderateScale(p, 0.5)}),
      ...(pt !== undefined && {paddingTop: moderateScale(pt, 0.5)}),
      ...(pb !== undefined && {paddingBottom: moderateScale(pb, 0.5)}),
      ...(pl !== undefined && {paddingLeft: moderateScale(pl, 0.5)}),
      ...(pr !== undefined && {paddingRight: moderateScale(pr, 0.5)}),
      ...(px !== undefined && {paddingHorizontal: moderateScale(px, 0.5)}),
      ...(py !== undefined && {
        paddingVertical: moderateVerticalScale(py, 0.5),
      }),
      ...(m !== undefined && {margin: moderateScale(m, 0.5)}),
      ...(mt !== undefined && {marginTop: moderateScale(mt, 0.5)}),
      ...(mb !== undefined && {marginBottom: moderateScale(mb, 0.5)}),
      ...(ml !== undefined && {marginLeft: moderateScale(ml, 0.5)}),
      ...(mr !== undefined && {marginRight: moderateScale(mr, 0.5)}),
      ...(mx !== undefined && {marginHorizontal: moderateScale(mx, 0.5)}),
      ...(my !== undefined && {marginVertical: moderateVerticalScale(my, 0.5)}),
      ...(flex !== undefined && {flex}),
      ...(pos !== undefined && {position: pos}),
      ...(fd !== undefined && {flexDirection: fd}),
      ...(center && {alignItems: 'center', justifyContent: 'center'}),
      ...(jc !== undefined && {justifyContent: jc}),
      ...(ai !== undefined && {alignItems: ai}),
      ...(fw !== undefined && {flexWrap: fw}),
      ...(display != undefined && {display: display}),
      ...(row && {flexDirection: 'row'}),
      ...(column && {flexDirection: 'column'}),
      ...(left && {left}),
      ...(top && {top}),
      ...(bottom && {bottom}),
      ...(right && {right}),
      ...(z && {zIndex: z}),
      ...(flexCenterRow && {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }),
      ...(flexCenterColumn && {
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      }),
      ...(gap !== undefined && {gap: moderateScale(gap, 0.5)}),
      ...(size != undefined && {height: size, width: size}),
    } as ViewStyle;

    return (
      <Animated.View
        ref={ref}
        style={[defaultStyles, style]}
        {...props}>
        {props.children}
      </Animated.View>
    );
  },
);

AnimatedView.displayName = 'AnimatedView';
