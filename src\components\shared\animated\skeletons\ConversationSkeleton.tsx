import { View } from '@components/native';
import { Skeleton } from './Skeleton';

export const ConversationSkeleton: React.FC = () => {
  return (
    <View flex={1} bg="background" p={20}>
      <Skeleton h={42} br={16} />
      <Skeleton w={100} mt={25} br={16} mb={20} h={20} />
      <View display="flex" fd="row" gap={12} ai="center">
        <Skeleton w={100} size={120} br={16} h={20} />
        <Skeleton w={100} size={120} br={16} h={20} />
        <Skeleton w={100} size={120} br={16} h={20} />
      </View>
      <Skeleton w={130} mt={25} br={16} mb={10} h={20} />
      <Skeleton h={65} mt={5} br={16} mb={20} />
      <Skeleton h={65} mt={5} br={16} mb={20} />
      <Skeleton h={65} mt={5} br={16} mb={20} />
      <Skeleton h={65} mt={5} br={16} mb={20} />
    </View>
  );
};
