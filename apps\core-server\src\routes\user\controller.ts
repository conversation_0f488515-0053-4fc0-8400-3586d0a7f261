import type { Request, Response, NextFunction } from "express";
import { eq, and, or, desc, count } from "drizzle-orm";

import {
  authValidator,
  bodyValidator,
  queryValidator,
} from "@/lib/validation-module.js";
import {
  validateUpdateUserDetails,
  validateCreateUserDetails,
  validateUserNameQuery,
  validateGetUserNameSuggestion,
} from "./validator.js";

import {
  addressTable,
  avatarTable,
  filesTable,
  reportsTable,
  userAddressTable,
  userDetails,
  users,
} from "@repo/db/schema";
import { Infer } from "@vinejs/vine/types";
import { ErrorResponse } from "@/lib/error-handlers-module/index.js";
import fileUpload from "express-fileupload";

// import db from "@repo/db";
import { defaultAvatarUrl } from "@repo/lib";
import db from "@repo/db";
import { suggestUniqueUsername } from "./lib.js";
import { UserRepo } from "@repo/repo";
import { Paginator } from "@/lib/pagination-module.js";

class UserController {
  @bodyValidator(validateCreateUserDetails)
  static async addUserDetails(req: Request, res: Response, next: NextFunction) {
    try {
      const { dob, username } = req.body as Infer<
        typeof validateCreateUserDetails
      >;

      console.log({ dob });
      const userId = req.user.id; // Get userId from authenticated request

      // Check if user exists

      const newUserDetails = await db.transaction(async (tx) => {
        const existingUser = await tx
          .select()
          .from(users)
          .where(eq(users.id, userId))
          .limit(1);

        if (!existingUser.length) {
          throw new Error("User not found.");
        }
        const [existingDetails] = await tx
          .select()
          .from(userDetails)
          .where(
            or(
              eq(userDetails.userName, username),
              eq(userDetails.userId, userId),
            ),
          )
          .limit(1);

        if (existingDetails) {
          throw new ErrorResponse("User Details already added", 409);
        }

        const [createdUserDetails] = await tx
          .insert(userDetails)
          .values([
            {
              userId,
              userName: username,
              avatarUrl: defaultAvatarUrl,
              dob: dob,
            },
          ])
          .returning();
        if (!createdUserDetails) {
          throw new Error("User details not created");
        }
        const user = await UserRepo.getPersonalUser({ userId }, tx)!;
        return user;
      });

      res.status(201).json({
        message: "User details added successfully.",
        data: newUserDetails,
        success: true,
      });
    } catch (error) {
      next(error); // Pass errors to global error handler
    }
  }
  @queryValidator(validateUserNameQuery)
  static async usernameAvailability(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const { username } = req.query as Infer<typeof validateUserNameQuery>;

      // const existingUser = await db.query.userDetails.findFirst({
      //   where: (data, { eq }) => eq(data.userName, username),
      // });
      const [existingUser] = await db
        .select()
        .from(userDetails)
        .where(eq(userDetails.userName, username))
        .limit(1);
      if (existingUser) {
        res.status(409).json({
          message: "Username not available",
          data: {
            available: false,
            username,
          },
        });
      } else {
        res.status(200).json({
          message: "Username available",
          data: {
            available: true,
            username,
          },
        });
      }
      return;
    } catch (error) {
      next(error);
    }
  }

  @queryValidator(validateGetUserNameSuggestion)
  static async getSuggestedUsername(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const { firstName, lastName } = req.query as Infer<
        typeof validateGetUserNameSuggestion
      >;
      const username = await suggestUniqueUsername(firstName, lastName ?? "");
      res.status(200).json({ data: { username }, success: true });
    } catch (error) {
      next(error);
    }
  }
  @authValidator()
  @bodyValidator(validateUpdateUserDetails)
  static async updateDetails(req: Request, res: Response, next: NextFunction) {
    try {
      const {
        bio,
        gender,
        dob,
        userName,
        avatarUrl,
        city,
        state,
        country,
        avatar,
        playlistLink,
        showMood,
      } = req.body as Infer<typeof validateUpdateUserDetails>;
      if (Object.keys(req.body).length === 0 && !req.files?.avatar) {
        throw new ErrorResponse("No fields to update", 422);
      }

      await db.transaction(async (tx) => {
        const [userData] = await tx
          .select({
            id: userDetails.id,
            address: {
              id: addressTable.id,
            },
          })
          .from(userDetails)
          .where(eq(userDetails.userId, req.user.id))
          .leftJoin(addressTable, eq(addressTable.id, userDetails.addressId));

        if (!userData) {
          throw new ErrorResponse("User details not set properly", 500);
        }

        if (!userData?.address?.id) {
          const [address] = await tx
            .insert(addressTable)
            .values({
              city,
              state,
              country,
            })
            .returning();
          if (!address) {
            throw new ErrorResponse("Address not created", 500);
          }
          await tx
            .update(userDetails)
            .set({ addressId: address.id })
            .where(eq(userDetails.userId, req.user.id));
          userData.address = address;
        } else if (city || state || country) {
          await tx
            .update(addressTable)
            .set({
              city,
              state,
              country,
            })
            .where(eq(addressTable.id, userData.address.id));
        }
        await tx
          .update(userDetails)
          .set({
            bio,
            gender,
            userName,
            avatarUrl: avatarUrl,
            dob: dob,
            showCurrentMood: showMood,
            playlistLink: playlistLink,
          })
          .where(eq(userDetails.userId, req.user.id));
      });

      res.status(200).json({ message: "User details updated successfully." });
    } catch (error) {
      next(error);
    }
  }

  @authValidator()
  static async me(req: Request, res: Response, next: NextFunction) {
    try {
      console.log("should've sent by now");

      const user = await UserRepo.getPersonalUser({ userId: req.user.id });
      if (!user) {
        throw new ErrorResponse("User not found", 404);
      }
      console.log(user);
      res.status(200).json({ data: user, success: true });
    } catch (error) {
      next(error);
    }
  }

  @authValidator()
  static async toggleVisibility(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const { visibility } = req.body as { visibility: "public" | "private" };
      const output = await db.transaction(async (tx) => {
        const [user] = await tx
          .select({ visibility: userDetails.visibility })
          .from(userDetails)
          .where(eq(userDetails.userId, req.user.id));
        if (!user) {
          throw new ErrorResponse("User not found", 404);
        }
        const newVis =
          visibility || user.visibility === "public" ? "private" : "public";
        const [response] = await tx
          .update(userDetails)
          .set({
            visibility: newVis,
          })
          .where(eq(userDetails.userId, req.user.id))
          .returning({
            visibility: userDetails.visibility,
          });
        return response;
      });
      res.json({ message: "Visibility updated successfully", data: output });
      return;
    } catch (error) {
      next(error);
    }
  }
  @authValidator()
  static async toggleNotifications(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const { value } = req.body as { value: boolean };
      const response = await db.transaction(async (tx) => {
        const [user] = await tx
          .select({ notifications: userDetails.notifications })
          .from(userDetails)
          .where(eq(userDetails.userId, req.user.id));
        if (!user) {
          throw new ErrorResponse("User not found", 404);
        }
        const notifications = value || !user.notifications;
        const [response] = await tx
          .update(userDetails)
          .set({
            notifications,
          })
          .where(eq(userDetails.userId, req.user.id))
          .returning({
            notifications: userDetails.notifications,
          });

        return response;
      });
      res.json({
        message: "Notifications updated successfully",
        data: response,
      });
      return;
    } catch (error) {
      next(error);
    }
  }

  static async getAvatarList(req: Request, res: Response, next: NextFunction) {
    try {
      const { limit, offset } = Paginator.getPage(req.query);
      const filter = eq(avatarTable.visibility, "public");
      const total = await Paginator.getTotalCount(
        db.select({ count: count() }).from(avatarTable).where(filter),
      );

      const rows = await db
        .select({
          id: avatarTable.id,
          url: avatarTable.url,
        })
        .from(avatarTable)
        .where(filter)
        .limit(limit)
        .offset(offset);

      const data = Paginator.paginate(req.query, rows, total);
      res.json({ data });
    } catch (error) {
      next(error);
    }
  }
}

export default UserController;
