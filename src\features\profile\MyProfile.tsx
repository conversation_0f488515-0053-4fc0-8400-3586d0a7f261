import { <PERSON><PERSON>, SafeAreaView, Text, View } from '@components/native';
import { Avatar } from '@components/shared';
import { Ripple } from '@components/shared/animated';
import { useAuth } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import { FlashList } from '@shopify/flash-list';
import React from 'react';
import { RefreshControlProps, ScrollView } from 'react-native';

export const MyProfileScreen = () => {
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <FlashList
        onRefresh={() => {}}
        refreshing={false}
        style={{ backgroundColor: 'red' }}
        ListHeaderComponent={ProfileHeader}
        data={Array.from({ length: 0 })}
        renderItem={({ item, index }) => (
          <View>
            <Text>item {index}</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

const ProfileHeader = () => {
  const user = useAuth()?.session?.user!;

  return (
    <View p={20}>
      {/* Avatar with profile percentage */}
      <View flexCenterColumn>
        <Avatar progress={60} />
        <Text fs="10" mt={20} color="neutral80">
          {user?.profilePercentage}% profile completed
        </Text>
      </View>

      {/* Posts , Healed , Followers , Following */}
      <View mt={8} display="flex" fd="row" jc="space-evenly">
        <Ripple style={{ padding: 20 }}>
          <View flexCenterColumn>
            <Text fw="600" color="neutral80">
              {user?.totalPostCount}
            </Text>
            <Text fs="10">Posts</Text>
          </View>
        </Ripple>
        <Ripple style={{ padding: 20 }}>
          <View flexCenterColumn>
            <Text fw="600" color="neutral80">
              {user?.totalHealCount}
            </Text>
            <Text fs="10">Healed</Text>
          </View>
        </Ripple>
        <Ripple style={{ padding: 20 }}>
          <View flexCenterColumn>
            <Text fw="600" color="neutral80">
              {user?.totalFollowers}
            </Text>
            <Text fs="10">Followers</Text>
          </View>
        </Ripple>
        <Ripple style={{ padding: 20 }}>
          <View flexCenterColumn>
            <Text fw="600" color="neutral80">
              {user?.totalFollowing}
            </Text>
            <Text fs="10">Following</Text>
          </View>
        </Ripple>
      </View>

      {/* Bio / Description */}
      <View mt={5}>
        <Text color="neutral80" fs="12" fw="600">
          Bio/Description
        </Text>
        <Text color="neutral70" fs="12" my={6}>
          {user?.bio}
        </Text>
      </View>

      {/* Update Profile and Questionaire */}
      <View display="flex" fd="row" gap={20} my={12}>
        <Button
          onPress={() => router.navigate('EditProfile')}
          isHorizontalAligned
        >
          Update Profile
        </Button>
        <Button
          onPress={() => {}}
          bg="purpleLight"
          color="purple700"
          bc="purple500"
          bw={1}
          isHorizontalAligned
        >
          Questionnaire
        </Button>
      </View>
      <View h={0.5} bg="neutral10" />
      {user?.totalPostCount > 0 && (
        <Text my={10} fw="500" fs="14" color="neutral80">
          Recent Posts
        </Text>
      )}
    </View>
  );
};
