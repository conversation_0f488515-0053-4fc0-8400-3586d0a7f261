import {Database} from '@nozbe/watermelondb';
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite';
import {schema} from './model/schemas';
import {UserModel} from './model/user-schema';

// Initialize the SQLite adapter
const adapter = new SQLiteAdapter({
  schema,
  // migrations, // Optional: Comment out during development if not using migrations
  dbName: 'stitch_db',
  jsi: true, // Enable JSI for better performance (works out of the box on iOS, requires setup on Android)
  onSetUpError: error => {
    console.error('WatermelonDB setup error:', error);
    // Optionally, trigger a user-facing error or retry logic
  },
});

// Initialize the WatermelonDB database
const database = new Database({
  adapter,
  modelClasses: [UserModel],
});

/**
 *
 * Function to drop the entire database
 *
 * */
export async function dropDatabase() {
  try {
    adapter.unsafeResetDatabase(r => {
      console.log('Deleted DB Result >>', r);
    });
    console.log('Database dropped successfully');

    console.log('Database reinitialized successfully');
  } catch (error) {
    console.error('Error dropping database:', error);
    throw error; // Rethrow to allow calling code to handle the error
  }
}

// dropDatabase();

export {adapter, database};
